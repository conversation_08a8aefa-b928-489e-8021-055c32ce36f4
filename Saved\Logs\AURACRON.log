﻿Log file open, 08/27/25 22:54:04
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=27732)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 35043
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/Game/AURACRON/AURACRON.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=7178dbae77724a0ca3a7771a124b1174 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.316231
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-AA5317FA4E2D95F4FC2792B7C3070E82
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogAssetRegistry: Display: Asset registry cache read as 73.0 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogConfig: Display: Loading IOS ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogConfig: Display: Loading VulkanPC ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin AndroidMedia
LogConfig: Display: Loading Android ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogConfig: Display: Loading Mac ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogConfig: Display: Loading Windows ini files took 0.12 seconds
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogConfig: Display: Loading TVOS ini files took 0.12 seconds
LogConfig: Display: Loading Unix ini files took 0.12 seconds
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogConfig: Display: Loading Linux ini files took 0.13 seconds
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin MLAdapter
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin EngineCameras
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin LandscapePatch
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=7178dbae77724a0ca3a7771a124b1174 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.28-01.54.04:751][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.28-01.54.04:751][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.28-01.54.04:751][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.28-01.54.04:751][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.28-01.54.04:751][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.28-01.54.04:751][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.28-01.54.04:751][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.28-01.54.04:751][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.28-01.54.04:751][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.28-01.54.04:753][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.28-01.54.04:753][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.28-01.54.04:753][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.28-01.54.04:753][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.28-01.54.04:753][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.28-01.54.04:754][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.28-01.54.04:754][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.28-01.54.04:754][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.28-01.54.04:754][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.28-01.54.04:756][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.28-01.54.04:756][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.28-01.54.04:757][  0]LogRHI: Using Default RHI: D3D12
[2025.08.28-01.54.04:757][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.28-01.54.04:757][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.28-01.54.04:761][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.28-01.54.04:761][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.28-01.54.04:838][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.28-01.54.04:856][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.28-01.54.04:856][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.28-01.54.04:856][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.28-01.54.04:856][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.28-01.54.04:856][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.28-01.54.04:861][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.28-01.54.04:861][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.28-01.54.04:861][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.28-01.54.04:861][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.28-01.54.04:861][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.28-01.54.04:861][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.28-01.54.04:861][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.28-01.54.04:861][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.28-01.54.04:863][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.28-01.54.04:863][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.28-01.54.04:863][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.28-01.54.04:863][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.28-01.54.04:863][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.28-01.54.04:863][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.28-01.54.05:045][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.28-01.54.05:045][  0]LogD3D11RHI:     AdapterLuid : 0 86560
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.28-01.54.05:048][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.28-01.54.05:048][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.28-01.54.05:048][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.28-01.54.05:048][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.28-01.54.05:048][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.28-01.54.05:048][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.28-01.54.05:048][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.28-01.54.05:048][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.28-01.54.05:048][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.28-01.54.05:048][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.28-01.54.05:048][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.28-01.54.05:048][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.28-01.54.05:048][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.28-01.54.05:048][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.28-01.54.05:048][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.28-01.54.05:048][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.28-01.54.05:048][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.28-01.54.05:048][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.28-01.54.05:048][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.28-01.54.05:048][  0]LogInit: Computer: TKT
[2025.08.28-01.54.05:048][  0]LogInit: User: tktca
[2025.08.28-01.54.05:048][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.28-01.54.05:048][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.28-01.54.05:048][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.28-01.54.05:048][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=36.9GB
[2025.08.28-01.54.05:048][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.28-01.54.05:048][  0]LogMemory: Process Physical Memory: 645.45 MB used, 708.62 MB peak
[2025.08.28-01.54.05:048][  0]LogMemory: Process Virtual Memory: 651.27 MB used, 694.06 MB peak
[2025.08.28-01.54.05:048][  0]LogMemory: Physical Memory: 18329.51 MB used,  14123.19 MB free, 32452.70 MB total
[2025.08.28-01.54.05:048][  0]LogMemory: Virtual Memory: 23349.44 MB used,  14392.39 MB free, 37741.82 MB total
[2025.08.28-01.54.05:048][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.28-01.54.05:051][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.28-01.54.05:053][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.28-01.54.05:053][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.28-01.54.05:053][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.28-01.54.05:053][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.28-01.54.05:058][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.28-01.54.05:344][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.28-01.54.05:345][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.28-01.54.05:445][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.28-01.54.05:445][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.28-01.54.05:445][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.28-01.54.05:445][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.28-01.54.05:452][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.28-01.54.05:454][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.28-01.54.05:458][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.28-01.54.05:458][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.28-01.54.05:559][  0]LogRHI: Using Default RHI: D3D12
[2025.08.28-01.54.05:559][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.28-01.54.05:559][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.28-01.54.05:559][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.28-01.54.05:559][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.28-01.54.05:559][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.28-01.54.05:559][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.28-01.54.05:559][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.28-01.54.05:559][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.28-01.54.05:560][  0]LogWindows: Attached monitors:
[2025.08.28-01.54.05:560][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY1' [PRIMARY]
[2025.08.28-01.54.05:560][  0]LogWindows: Found 1 attached monitors.
[2025.08.28-01.54.05:560][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.28-01.54.05:560][  0]LogRHI: RHI Adapter Info:
[2025.08.28-01.54.05:560][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.05:560][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.28-01.54.05:560][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.28-01.54.05:560][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.28-01.54.05:560][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.28-01.54.05:560][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.28-01.54.05:560][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.28-01.54.05:560][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.28-01.54.05:733][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.28-01.54.05:756][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.28-01.54.05:756][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.28-01.54.05:758][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.28-01.54.05:758][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.28-01.54.05:758][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.28-01.54.05:758][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.28-01.54.05:758][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.28-01.54.05:769][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.28-01.54.05:772][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.28-01.54.05:772][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.28-01.54.05:772][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.28-01.54.05:772][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.28-01.54.05:772][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.28-01.54.05:776][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all'
[2025.08.28-01.54.05:776][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all" ]
[2025.08.28-01.54.05:788][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.28-01.54.05:788][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.28-01.54.05:788][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.28-01.54.05:788][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.28-01.54.05:788][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.28-01.54.05:788][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.28-01.54.05:788][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.28-01.54.05:788][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.28-01.54.05:789][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.28-01.54.05:789][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.28-01.54.05:820][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.28-01.54.05:834][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.28-01.54.05:834][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.28-01.54.05:847][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.28-01.54.05:847][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.28-01.54.05:847][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.28-01.54.05:847][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.28-01.54.05:859][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.28-01.54.05:859][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.28-01.54.05:859][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.28-01.54.05:859][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.28-01.54.05:872][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.28-01.54.05:872][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.28-01.54.05:886][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.28-01.54.05:886][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.28-01.54.05:886][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.28-01.54.05:886][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.28-01.54.05:886][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.28-01.54.05:921][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.28-01.54.05:926][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.28-01.54.05:926][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.28-01.54.05:929][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.28-01.54.05:929][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.28-01.54.05:929][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.28-01.54.05:929][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.28-01.54.05:929][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.28-01.54.06:116][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.28-01.54.06:116][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.28-01.54.06:116][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.28-01.54.06:118][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.28-01.54.06:119][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.28-01.54.06:119][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.28-01.54.06:119][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.28-01.54.06:749][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.28-01.54.06:827][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.28-01.54.06:827][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.709 seconds
[2025.08.28-01.54.06:829][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.28-01.54.06:838][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.28-01.54.06:838][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=1438.70MBs, RandomWriteSpeed=103.43MBs. Assigned SpeedClass 'Local'
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.28-01.54.06:840][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.28-01.54.06:842][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/15444/).
[2025.08.28-01.54.06:842][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/B36E035A4994994C811F20BD206B45DC/'.
[2025.08.28-01.54.06:842][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.28-01.54.06:843][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.28-01.54.06:843][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.28-01.54.06:845][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.28-01.54.06:845][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.28-01.54.08:014][  0]LogSlate: Using FreeType 2.10.0
[2025.08.28-01.54.08:014][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.28-01.54.08:017][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.28-01.54.08:017][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.28-01.54.08:017][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.28-01.54.08:017][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.28-01.54.08:034][  0]LogAssetRegistry: FAssetRegistry took 0.0018 seconds to start up
[2025.08.28-01.54.08:035][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.28-01.54.08:129][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.28-01.54.08:266][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.28-01.54.08:266][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.28-01.54.08:289][  0]LogDeviceProfileManager: Active device profile: [00000285E8194D80][00000285DF84C800 66] WindowsEditor
[2025.08.28-01.54.08:289][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.28-01.54.08:293][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.28-01.54.08:295][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.28-01.54.08:295][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.28-01.54.08:295][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.28-01.54.08:300][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.28-01.54.08:300][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.28-01.54.08:300][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.28-01.54.08:301][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT'
[2025.08.28-01.54.08:301][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.28-01.54.08:326][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:326][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.28-01.54.08:326][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:326][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:326][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.28-01.54.08:327][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:327][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:327][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.28-01.54.08:327][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.28-01.54.08:329][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.28-01.54.08:329][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:331][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:331][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:331][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.28-01.54.08:331][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.28-01.54.08:332][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.28-01.54.08:332][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:362][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.28-01.54.08:362][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:374][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.28-01.54.08:374][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:385][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.28-01.54.08:385][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.28-01.54.08:494][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.28-01.54.08:494][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.28-01.54.08:494][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.28-01.54.08:494][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.28-01.54.08:494][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.28-01.54.08:967][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.28-01.54.08:999][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.28-01.54.08:999][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.28-01.54.08:999][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.28-01.54.08:999][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.28-01.54.09:004][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.28-01.54.09:004][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.28-01.54.09:004][  0]LogLiveCoding: Display: First instance in process group "UE_AURACRON_0xa5ca6502", spawning console
[2025.08.28-01.54.09:007][  0]LogLiveCoding: Display: Waiting for server
[2025.08.28-01.54.09:025][  0]LogSlate: Border
[2025.08.28-01.54.09:025][  0]LogSlate: BreadcrumbButton
[2025.08.28-01.54.09:025][  0]LogSlate: Brushes.Title
[2025.08.28-01.54.09:025][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.28-01.54.09:025][  0]LogSlate: Default
[2025.08.28-01.54.09:025][  0]LogSlate: Icons.Save
[2025.08.28-01.54.09:025][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.28-01.54.09:025][  0]LogSlate: ListView
[2025.08.28-01.54.09:025][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.28-01.54.09:025][  0]LogSlate: SoftwareCursor_Grab
[2025.08.28-01.54.09:025][  0]LogSlate: TableView.DarkRow
[2025.08.28-01.54.09:025][  0]LogSlate: TableView.Row
[2025.08.28-01.54.09:025][  0]LogSlate: TreeView
[2025.08.28-01.54.09:101][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.28-01.54.09:104][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 3.024 ms
[2025.08.28-01.54.09:121][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.28-01.54.09:121][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.28-01.54.09:121][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.28-01.54.09:150][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.28-01.54.09:176][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.28-01.54.09:179][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.28-01.54.09:179][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.28-01.54.09:179][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:49358'.
[2025.08.28-01.54.09:186][  0]LogUdpMessaging: Display: Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
[2025.08.28-01.54.09:186][  0]LogUdpMessaging: Display: Added local interface '172.17.96.1' to multicast group '230.0.0.1:6666'
[2025.08.28-01.54.09:189][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.28-01.54.09:308][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.28-01.54.09:308][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT: Interface availability:
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   RDG: no
[2025.08.28-01.54.09:406][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.28-01.54.09:521][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.28-01.54.09:521][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.28-01.54.09:581][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.28-01.54.09:782][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.28-01.54.09:782][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.28-01.54.09:801][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.28-01.54.09:909][  0]LogMLAdapter: Warning: Neural network asset data not set
[2025.08.28-01.54.10:152][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 8B26C108E5094BD2800000000000CB00 | Instance: FC33D9E744064A7F53D802A1B2EC9327 (TKT-15444).
[2025.08.28-01.54.10:184][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.28-01.54.10:209][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.28-01.54.10:209][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.28-01.54.10:214][  0]LogTimingProfiler: Initialize
[2025.08.28-01.54.10:214][  0]LogTimingProfiler: OnSessionChanged
[2025.08.28-01.54.10:214][  0]LoadingProfiler: Initialize
[2025.08.28-01.54.10:214][  0]LoadingProfiler: OnSessionChanged
[2025.08.28-01.54.10:214][  0]LogNetworkingProfiler: Initialize
[2025.08.28-01.54.10:214][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.28-01.54.10:214][  0]LogMemoryProfiler: Initialize
[2025.08.28-01.54.10:214][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.28-01.54.10:248][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.28-01.54.10:337][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.28-01.54.10:340][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.28-01.54.10:345][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.28-01.54.10:345][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.28-01.54.10:458][  0]SourceControl: Controle de revisão desabilitado
[2025.08.28-01.54.10:465][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.28-01.54.10:465][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.28-01.54.10:465][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.28-01.54.10:465][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.28-01.54.10:471][  0]SourceControl: Controle de revisão desabilitado
[2025.08.28-01.54.10:665][  0]LogCollectionManager: Loaded 0 collections in 0.001959 seconds
[2025.08.28-01.54.10:669][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.00s
[2025.08.28-01.54.10:672][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.28-01.54.10:674][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.00s
[2025.08.28-01.54.10:710][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.28-01.54.10:757][  0]LogUObjectArray: 44826 objects as part of root set at end of initial load.
[2025.08.28-01.54.10:757][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.28-01.54.10:906][  0]LogAutomationTest: Error: Condition failed
[2025.08.28-01.54.10:906][  0]LogAutomationTest: Error: Condition failed
[2025.08.28-01.54.10:906][  0]LogAutomationTest: Error: Condition failed
[2025.08.28-01.54.10:907][  0]LogEngine: Initializing Engine...
[2025.08.28-01.54.11:041][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.28-01.54.11:041][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.28-01.54.11:041][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.28-01.54.11:041][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.28-01.54.11:042][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.28-01.54.11:045][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.28-01.54.11:335][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.28-01.54.11:358][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.28-01.54.11:375][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.28-01.54.11:396][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.08.28-01.54.11:396][  0]LogInit: Texture streaming: Enabled
[2025.08.28-01.54.11:408][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.28-01.54.11:416][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.28-01.54.11:431][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.28-01.54.11:432][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.28-01.54.11:434][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.28-01.54.11:434][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.28-01.54.11:434][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.28-01.54.11:434][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.28-01.54.11:434][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.28-01.54.11:434][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.28-01.54.11:434][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.28-01.54.11:448][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.28-01.54.11:921][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.28-01.54.11:923][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.28-01.54.11:923][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.28-01.54.11:923][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.28-01.54.11:925][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.28-01.54.11:925][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.28-01.54.11:926][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.28-01.54.11:926][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.28-01.54.11:926][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.28-01.54.11:926][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.28-01.54.11:926][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.28-01.54.11:931][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.28-01.54.11:939][  0]LogInit: Undo buffer set to 256 MB
[2025.08.28-01.54.11:939][  0]LogInit: Transaction tracking system initialized
[2025.08.28-01.54.11:947][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.54.11:982][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.28-01.54.12:016][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.28-01.54.12:093][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/' took 0.00s
[2025.08.28-01.54.12:134][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.28-01.54.12:138][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.28-01.54.12:140][  0]LogPython: Using Python 3.11.8
[2025.08.28-01.54.12:169][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.28-01.54.12:731][  0]LogMLAdapter: Creating MLAdapter manager of class MLAdapterManager
[2025.08.28-01.54.12:766][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.28-01.54.12:854][  0]LogEditorDataStorage: Initializing
[2025.08.28-01.54.12:858][  0]LogEditorDataStorage: Initialized
[2025.08.28-01.54.12:859][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.28-01.54.12:860][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.28-01.54.12:961][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.28-01.54.12:961][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.28-01.54.12:967][  0]SourceControl: Controle de revisão desabilitado
[2025.08.28-01.54.12:967][  0]LogUnrealEdMisc: Loading editor; pre map load, took 9.012
[2025.08.28-01.54.12:968][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.28-01.54.12:970][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.28-01.54.12:970][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.28-01.54.12:988][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.28-01.54.12:989][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.08.28-01.54.12:999][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.28-01.54.12:999][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.28-01.54.13:001][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.28-01.54.13:001][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.28-01.54.13:001][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.28-01.54.13:132][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.28-01.54.13:140][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.28-01.54.13:144][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.28-01.54.13:151][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.28-01.54.13:151][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.28-01.54.13:151][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.28-01.54.13:156][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.28-01.54.13:286][  0]LogWorldPartition: Display: WorldPartition initialize took 285.558 ms
[2025.08.28-01.54.13:468][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.28-01.54.13:488][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.28-01.54.13:491][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.28-01.54.13:492][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 1,201ms para ser concluída.
[2025.08.28-01.54.13:501][  0]LogUnrealEdMisc: Total Editor Startup Time, took 9.546
[2025.08.28-01.54.13:602][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.28-01.54.13:660][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.28-01.54.14:002][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.28-01.54.14:306][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.28-01.54.14:526][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.28-01.54.14:840][  0]LogAssetRegistry: Display: Asset registry cache written as 73.0 MiB to ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin
[2025.08.28-01.54.14:858][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.28-01.54.15:201][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:202][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.28-01.54.15:202][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:202][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.28-01.54.15:202][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:202][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.28-01.54.15:202][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:202][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.28-01.54.15:203][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:203][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.28-01.54.15:207][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:207][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.28-01.54.15:207][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:207][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.28-01.54.15:208][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:209][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.28-01.54.15:209][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:211][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.28-01.54.15:211][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.28-01.54.15:212][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.28-01.54.16:332][  0]LogSlate: Took 0.000362 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.28-01.54.16:337][  0]LogSlate: Took 0.000238 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.28-01.54.16:399][  0]LogSlate: Took 0.000237 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.28-01.54.16:542][  0]LogStall: Startup...
[2025.08.28-01.54.16:547][  0]LogStall: Startup complete.
[2025.08.28-01.54.16:552][  0]LogLoad: (Engine Initialization) Total time: 12.60 seconds
[2025.08.28-01.54.17:210][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.28-01.54.17:210][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.28-01.54.17:266][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.28-01.54.17:266][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.28-01.54.17:300][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.28-01.54.17:304][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 38.002 ms
[2025.08.28-01.54.17:589][  1]LogAssetRegistry: AssetRegistryGather time 0.1275s: AssetDataDiscovery 0.0123s, AssetDataGather 0.0526s, StoreResults 0.0626s. Wall time 9.5570s.
	NumCachedDirectories 1419. NumUncachedDirectories 21. NumCachedFiles 7457. NumUncachedFiles 0.
	BackgroundTickInterruptions 2.
[2025.08.28-01.54.17:623][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.28-01.54.17:632][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.28-01.54.17:632][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.28-01.54.17:855][  2]LogSourceControl: Uncontrolled asset discovery finished in 0.222491 seconds (Found 7433 uncontrolled assets)
[2025.08.28-01.54.22:968][ 53]LogSlate: Took 0.000206 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.28-01.54.24:036][ 65]LogUObjectHash: Compacting FUObjectHashTables data took   1.00ms
[2025.08.28-01.54.24:039][ 65]Cmd: MAP LOAD FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.28-01.54.24:096][ 65]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.28-01.54.24:096][ 65]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.28-01.54.24:096][ 65]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.28-01.54.24:125][ 65]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.28-01.54.24:140][ 65]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.08.28-01.54.24:142][ 65]LogStreaming: Display: FlushAsyncLoading(476): 1 QueuedPackages, 0 AsyncPackages
[2025.08.28-01.54.24:144][ 65]LogAudio: Display: Audio Device (ID: 1) registered with world 'AURACRON'.
[2025.08.28-01.54.24:144][ 65]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON
[2025.08.28-01.54.24:184][ 65]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.28-01.54.24:212][ 65]LogUObjectHash: Compacting FUObjectHashTables data took   1.13ms
[2025.08.28-01.54.24:226][ 65]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.28-01.54.24:226][ 65]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,171ms para ser concluída.
[2025.08.28-01.54.24:229][ 65]LogSlate: Window 'Abrir nível' being destroyed
[2025.08.28-01.54.28:180][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.54.28:180][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.54.28:180][117]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.54.28:281][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.54.28:281][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.54.28:281][117]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.28-01.54.28:281][117]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.28-01.54.28:291][117]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
[2025.08.28-01.54.28:295][117]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		]
	}
}
[2025.08.28-01.54.28:296][117]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1072
[2025.08.28-01.54.28:296][117]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.54.59:813][322]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.54.59:813][322]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.54.59:813][322]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.54.59:913][323]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.54.59:913][323]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.54.59:913][323]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "PlanicieRadiante_Base", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-01.54.59:913][323]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-01.55.00:170][323]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-01.55.00:170][323]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "PlanicieRadiante_Base",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-01.55.00:170][323]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 197
[2025.08.28-01.55.00:170][323]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.55.12:958][435]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.12:958][435]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.12:958][435]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.55.13:060][436]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.13:060][436]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.13:060][436]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "FirmamentoZephyr_Base", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 3000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-01.55.13:060][436]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-01.55.13:362][436]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-01.55.13:362][436]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "FirmamentoZephyr_Base",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 3000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-01.55.13:362][436]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 197
[2025.08.28-01.55.13:362][436]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.55.20:210][530]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.20:210][530]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.20:210][530]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.55.20:311][530]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.20:311][530]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.20:311][530]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AbismoUmbral_Base", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 5000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-01.55.20:311][530]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-01.55.20:390][530]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-01.55.20:390][530]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "AbismoUmbral_Base",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 5000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-01.55.20:390][530]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 193
[2025.08.28-01.55.20:390][530]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.55.31:479][609]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.31:479][609]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.31:479][609]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.55.31:581][609]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.31:581][609]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.31:581][609]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "PlanicieRadiante_Terrain", "layer_index": 0, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 0.0}, "heightmap_settings": {"noise_type": "perlin", "frequency": 0.01, "amplitude": 200, "octaves": 4, "lacunarity": 2, "persistence": 0.5}}}
[2025.08.28-01.55.31:581][609]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.28-01.55.31:609][609]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.28-01.55.31:609][609]LogJson: Warning: Field use_pcg was not found.
[2025.08.28-01.55.31:609][609]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.31:609][609]LogJson: Warning: Field enable_nanite was not found.
[2025.08.28-01.55.31:609][609]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.31:609][609]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.28-01.55.31:609][609]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.31:641][609]LogTemp: CreateLayerMaterial: Created material for layer 0 with theme color (R=1.000000,G=0.800000,B=0.200000,A=1.000000)
[2025.08.28-01.55.31:641][609]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.31:670][609]LogTemp: CreateLayerMaterial: Created material for layer 1 with theme color (R=0.200000,G=0.800000,B=1.000000,A=1.000000)
[2025.08.28-01.55.31:670][609]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.31:704][609]LogTemp: CreateLayerMaterial: Created material for layer 2 with theme color (R=0.400000,G=0.200000,B=0.800000,A=1.000000)
[2025.08.28-01.55.31:704][609]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.31:704][609]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for PlanicieRadiante_Terrain
[2025.08.28-01.55.31:704][609]LogTemp: HandleCreateProceduralLandscape: Created landscape PlanicieRadiante_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.28-01.55.31:704][609]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "PlanicieRadiante_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-22.55.31"
	}
}
[2025.08.28-01.55.31:704][609]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 335
[2025.08.28-01.55.31:704][609]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.55.45:789][820]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.45:789][820]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.45:789][820]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.55.45:890][820]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.45:890][820]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.45:890][820]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "FirmamentoZephyr_Terrain", "layer_index": 1, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 2000.0}, "heightmap_settings": {"noise_type": "simplex", "frequency": 0.008, "amplitude": 150, "octaves": 3, "lacunarity": 2.2, "persistence": 0.6}}}
[2025.08.28-01.55.45:890][820]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.28-01.55.45:974][820]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.28-01.55.45:974][820]LogJson: Warning: Field use_pcg was not found.
[2025.08.28-01.55.45:974][820]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.45:974][820]LogJson: Warning: Field enable_nanite was not found.
[2025.08.28-01.55.45:974][820]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.45:974][820]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.28-01.55.45:974][820]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.46:005][820]LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.46:037][820]LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.46:070][820]LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.46:070][820]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for FirmamentoZephyr_Terrain
[2025.08.28-01.55.46:070][820]LogTemp: HandleCreateProceduralLandscape: Created landscape FirmamentoZephyr_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.28-01.55.46:071][820]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "FirmamentoZephyr_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-22.55.46"
	}
}
[2025.08.28-01.55.46:071][820]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 335
[2025.08.28-01.55.46:071][820]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.55.56:136][950]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.56:136][950]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.56:136][950]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.55.56:236][950]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.55.56:236][950]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.55.56:236][950]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "AbismoUmbral_Terrain", "layer_index": 2, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 4000.0}, "heightmap_settings": {"noise_type": "ridged", "frequency": 0.012, "amplitude": 300, "octaves": 5, "lacunarity": 1.8, "persistence": 0.4}}}
[2025.08.28-01.55.56:236][950]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.28-01.55.56:350][950]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.28-01.55.56:350][950]LogJson: Warning: Field use_pcg was not found.
[2025.08.28-01.55.56:351][950]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.56:351][950]LogJson: Warning: Field enable_nanite was not found.
[2025.08.28-01.55.56:351][950]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.56:351][950]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.28-01.55.56:351][950]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-01.55.56:379][950]LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.56:408][950]LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.56:438][950]LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-01.55.56:439][950]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for AbismoUmbral_Terrain
[2025.08.28-01.55.56:439][950]LogTemp: HandleCreateProceduralLandscape: Created landscape AbismoUmbral_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.28-01.55.56:439][950]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "AbismoUmbral_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-22.55.56"
	}
}
[2025.08.28-01.55.56:439][950]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 331
[2025.08.28-01.55.56:439][950]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.56.06:840][982]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.56.06:840][982]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.56.06:840][982]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.56.06:844][982]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.28-01.56.06:941][982]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.56.06:941][982]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.56.06:941][982]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "TopLane_PlanicieRadiante", "lane_type": "top", "start_location": {"x": -7000.0, "y": 7000.0, "z": 100.0}, "end_location": {"x": 7000.0, "y": -7000.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 20, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
[2025.08.28-01.56.06:941][982]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-01.56.07:019][982]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-01.56.07:019][982]LogJson: Warning: Field layer_index was not found.
[2025.08.28-01.56.07:019][982]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.56.07:024][982]LogTemp: GenerateLaneGeometryWithFlow: Generated lane TopLane_PlanicieRadiante with 45 vertices, 64 triangles
[2025.08.28-01.56.07:027][982]LogTemp: CreateRobustProceduralMesh: Created mesh TopLane_PlanicieRadiante with 56 vertices
[2025.08.28-01.56.07:027][982]LogTemp: HandleCreateLaneGeometry: Created lane TopLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
[2025.08.28-01.56.07:027][982]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "TopLane_PlanicieRadiante",
		"lane_width": 800,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-22.56.07"
	}
}
[2025.08.28-01.56.07:027][982]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
[2025.08.28-01.56.07:027][982]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.56.22:600][253]LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
[2025.08.28-01.56.25:012][253]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.08.28-01.56.25:035][253]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.56.25:105][253]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.28-01.56.25:146][253]LogUObjectHash: Compacting FUObjectHashTables data took   0.25ms
[2025.08.28-01.56.25:167][253]LogSavePackage: Moving output files for package: /Game/AURACRON
[2025.08.28-01.56.25:168][253]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON53C8CBD8445B9918AFFAB8AABCD69898.tmp' to '../../../../../../Game/AURACRON/Content/AURACRON.umap'
[2025.08.28-01.56.25:179][253]LogFileHelpers: Saving map 'AURACRON' took 0.076
[2025.08.28-01.56.25:205][253]LogFileHelpers: InternalPromptForCheckoutAndSave took 169.408 ms
[2025.08.28-01.56.25:268][253]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.56.25:268][253]LogContentValidation: Enabled validators:
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.56.25:268][253]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.56.25:269][253]AssetCheck: /Game/AURACRON Validando ativo
[2025.08.28-01.56.30:483][309]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.56.30:483][309]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.56.30:483][309]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.56.30:584][309]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.56.30:584][309]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.56.30:584][309]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "MidLane_PlanicieRadiante", "lane_type": "middle", "start_location": {"x": -6500.0, "y": 0.0, "z": 100.0}, "end_location": {"x": 6500.0, "y": 0.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 18, "elevation_curve": "flat", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
[2025.08.28-01.56.30:584][309]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-01.56.30:664][309]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-01.56.30:664][309]LogJson: Warning: Field layer_index was not found.
[2025.08.28-01.56.30:664][309]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.56.30:664][309]LogTemp: GenerateLaneGeometryWithFlow: Generated lane MidLane_PlanicieRadiante with 45 vertices, 64 triangles
[2025.08.28-01.56.30:665][309]LogTemp: CreateRobustProceduralMesh: Created mesh MidLane_PlanicieRadiante with 56 vertices
[2025.08.28-01.56.30:665][309]LogTemp: HandleCreateLaneGeometry: Created lane MidLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
[2025.08.28-01.56.30:665][309]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "MidLane_PlanicieRadiante",
		"lane_width": 800,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-22.56.30"
	}
}
[2025.08.28-01.56.30:665][309]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
[2025.08.28-01.56.30:665][309]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.01:956][869]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.01:956][869]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.01:956][869]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.57.02:058][870]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.02:058][870]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.02:058][870]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "BotLane_PlanicieRadiante", "lane_type": "bottom", "start_location": {"x": -7000.0, "y": -7000.0, "z": 100.0}, "end_location": {"x": 7000.0, "y": 7000.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 19, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
[2025.08.28-01.57.02:058][870]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-01.57.02:376][870]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-01.57.02:376][870]LogJson: Warning: Field layer_index was not found.
[2025.08.28-01.57.02:376][870]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.57.02:376][870]LogTemp: GenerateLaneGeometryWithFlow: Generated lane BotLane_PlanicieRadiante with 45 vertices, 64 triangles
[2025.08.28-01.57.02:376][870]LogTemp: CreateRobustProceduralMesh: Created mesh BotLane_PlanicieRadiante with 56 vertices
[2025.08.28-01.57.02:376][870]LogTemp: HandleCreateLaneGeometry: Created lane BotLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
[2025.08.28-01.57.02:378][870]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "BotLane_PlanicieRadiante",
		"lane_width": 800,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-22.57.02"
	}
}
[2025.08.28-01.57.02:378][870]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
[2025.08.28-01.57.02:378][870]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.12:337][994]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.12:337][994]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.12:337][994]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.57.12:438][994]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.12:439][994]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.12:439][994]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T1", "tower_type": "basic", "location": {"x": -5000.0, "y": 5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
[2025.08.28-01.57.12:439][994]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.57.12:576][994]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.57.12:576][994]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.57.12:576][994]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.57.12:662][994]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.08.28-01.57.12:664][994]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
[2025.08.28-01.57.12:676][994]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T1 spawned in world at location (-5000.0, 5000.0, 200.0)
[2025.08.28-01.57.12:676][994]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.57.12:715][994]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1] ([2] browsable assets)...
[2025.08.28-01.57.12:716][994]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.08.28-01.57.12:770][994]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.BP_Torre_Azul_Top_T1]
[2025.08.28-01.57.12:770][994]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1]
[2025.08.28-01.57.12:770][994]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.uasset" SILENT=true
[2025.08.28-01.57.12:794][994]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
[2025.08.28-01.57.12:794][994]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T18A68FE974C533907FA974C8F46C09DA9.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.uasset'
[2025.08.28-01.57.12:815][994]LogFileHelpers: InternalPromptForCheckoutAndSave took 138.019 ms (total: 307.427 ms)
[2025.08.28-01.57.12:815][994]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
[2025.08.28-01.57.12:815][994]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.57.12:815][994]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Top_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.57.12",
		"location":
		{
			"x": -5000,
			"y": 5000,
			"z": 200
		}
	}
}
[2025.08.28-01.57.12:815][994]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
[2025.08.28-01.57.12:815][994]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.12:831][995]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.57.12:963][995]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.57.12:963][995]LogContentValidation: Enabled validators:
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.57.12:963][995]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.57.12:963][995]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1 Validando ativo
[2025.08.28-01.57.34:876][260]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.34:876][260]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.34:876][260]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.57.34:978][260]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.34:978][260]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.34:978][260]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T2", "tower_type": "advanced", "location": {"x": -3500.0, "y": 3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
[2025.08.28-01.57.34:978][260]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.57.34:979][260]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.57.34:979][260]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.57.34:979][260]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.57.35:001][260]LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
[2025.08.28-01.57.35:005][260]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
[2025.08.28-01.57.35:005][260]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T2 spawned in world at location (-3500.0, 3500.0, 200.0)
[2025.08.28-01.57.35:005][260]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.57.35:056][260]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2] ([2] browsable assets)...
[2025.08.28-01.57.35:056][260]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.08.28-01.57.35:091][260]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.BP_Torre_Azul_Top_T2]
[2025.08.28-01.57.35:091][260]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2]
[2025.08.28-01.57.35:091][260]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.uasset" SILENT=true
[2025.08.28-01.57.35:101][260]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
[2025.08.28-01.57.35:101][260]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T2D719D30348C088B52A4A78A545AD0CCF.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.uasset'
[2025.08.28-01.57.35:119][260]LogFileHelpers: InternalPromptForCheckoutAndSave took 114.050 ms (total: 421.478 ms)
[2025.08.28-01.57.35:119][260]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
[2025.08.28-01.57.35:119][260]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-01.57.35:119][260]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Top_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.57.35",
		"location":
		{
			"x": -3500,
			"y": 3500,
			"z": 200
		}
	}
}
[2025.08.28-01.57.35:119][260]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
[2025.08.28-01.57.35:119][260]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.35:131][260]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.57.35:369][261]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.57.35:369][261]LogContentValidation: Enabled validators:
[2025.08.28-01.57.35:369][261]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.57.35:369][261]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.57.35:369][261]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.57.35:369][261]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.57.35:371][261]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.57.35:371][261]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.57.35:371][261]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2 Validando ativo
[2025.08.28-01.57.47:403][462]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.47:403][462]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.47:403][462]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.57.47:503][462]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.47:503][462]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.47:503][462]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Mid_T1", "tower_type": "basic", "location": {"x": -4000.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
[2025.08.28-01.57.47:503][462]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.57.47:669][462]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.57.47:669][462]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.57.47:670][462]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.57.47:691][462]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.28-01.57.47:693][462]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Mid_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
[2025.08.28-01.57.47:694][462]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Mid_T1 spawned in world at location (-4000.0, 0.0, 200.0)
[2025.08.28-01.57.47:694][462]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.57.47:733][462]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1] ([2] browsable assets)...
[2025.08.28-01.57.47:733][462]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.08.28-01.57.47:769][462]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.BP_Torre_Azul_Mid_T1]
[2025.08.28-01.57.47:769][462]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1]
[2025.08.28-01.57.47:769][462]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.uasset" SILENT=true
[2025.08.28-01.57.47:782][462]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
[2025.08.28-01.57.47:782][462]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Mid_T1FF6F0E9440FBD5A03178CB951F6AF04E.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.uasset'
[2025.08.28-01.57.47:798][462]LogFileHelpers: InternalPromptForCheckoutAndSave took 102.878 ms (total: 524.356 ms)
[2025.08.28-01.57.47:798][462]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Mid_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
[2025.08.28-01.57.47:798][462]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Mid_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.57.47:798][462]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Mid_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.57.47",
		"location":
		{
			"x": -4000,
			"y": 0,
			"z": 200
		}
	}
}
[2025.08.28-01.57.47:798][462]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 452
[2025.08.28-01.57.47:798][462]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.47:807][463]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.57.48:052][463]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.57.48:052][463]LogContentValidation: Enabled validators:
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.57.48:052][463]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.57.48:052][463]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1 Validando ativo
[2025.08.28-01.57.54:742][484]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.54:742][484]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.54:742][484]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.54:844][484]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.57.54:844][484]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.57.54:844][484]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Bot_T1", "tower_type": "basic", "location": {"x": -5000.0, "y": -5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
[2025.08.28-01.57.54:844][484]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.57.55:003][484]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.57.55:003][484]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.57.55:003][484]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.57.55:022][484]LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
[2025.08.28-01.57.55:025][484]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Bot_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
[2025.08.28-01.57.55:026][484]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Bot_T1 spawned in world at location (-5000.0, -5000.0, 200.0)
[2025.08.28-01.57.55:026][484]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.57.55:083][484]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1] ([2] browsable assets)...
[2025.08.28-01.57.55:083][484]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.08.28-01.57.55:116][484]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.BP_Torre_Azul_Bot_T1]
[2025.08.28-01.57.55:116][484]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1]
[2025.08.28-01.57.55:116][484]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.uasset" SILENT=true
[2025.08.28-01.57.55:126][484]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
[2025.08.28-01.57.55:126][484]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Bot_T16BB918784E6E832CEBC2BBA428CCA1D4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.uasset'
[2025.08.28-01.57.55:140][484]LogFileHelpers: InternalPromptForCheckoutAndSave took 114.562 ms (total: 638.918 ms)
[2025.08.28-01.57.55:140][484]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Bot_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
[2025.08.28-01.57.55:140][484]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Bot_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.57.55:140][484]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Bot_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.57.55",
		"location":
		{
			"x": -5000,
			"y": -5000,
			"z": 200
		}
	}
}
[2025.08.28-01.57.55:140][484]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 456
[2025.08.28-01.57.55:140][484]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.57.55:149][485]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.57.55:383][485]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.57.55:383][485]LogContentValidation: Enabled validators:
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.57.55:383][485]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.57.55:383][485]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1 Validando ativo
[2025.08.28-01.58.03:894][511]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.03:894][511]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.03:894][511]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.58.03:995][511]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.03:995][511]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.03:995][511]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Top_T1", "tower_type": "basic", "location": {"x": 5000.0, "y": -5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
[2025.08.28-01.58.03:995][511]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.58.04:004][511]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.58.04:004][511]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.58.04:004][511]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.58.04:024][511]LogUObjectHash: Compacting FUObjectHashTables data took   0.62ms
[2025.08.28-01.58.04:027][511]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Top_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
[2025.08.28-01.58.04:028][511]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Top_T1 spawned in world at location (5000.0, -5000.0, 200.0)
[2025.08.28-01.58.04:028][511]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.58.04:080][511]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1] ([2] browsable assets)...
[2025.08.28-01.58.04:081][511]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.08.28-01.58.04:113][511]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.BP_Torre_Vermelha_Top_T1]
[2025.08.28-01.58.04:113][511]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1]
[2025.08.28-01.58.04:113][511]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.uasset" SILENT=true
[2025.08.28-01.58.04:130][511]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
[2025.08.28-01.58.04:130][511]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Top_T1CFF497EA44F77E1EF2EAF1B158EAFB7A.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.uasset'
[2025.08.28-01.58.04:149][511]LogFileHelpers: InternalPromptForCheckoutAndSave took 122.320 ms (total: 761.239 ms)
[2025.08.28-01.58.04:149][511]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Top_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
[2025.08.28-01.58.04:149][511]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Top_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.58.04:149][511]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Top_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.58.04",
		"location":
		{
			"x": 5000,
			"y": -5000,
			"z": 200
		}
	}
}
[2025.08.28-01.58.04:149][511]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 459
[2025.08.28-01.58.04:150][511]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.58.04:160][511]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.58.04:395][512]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.58.04:395][512]LogContentValidation: Enabled validators:
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.58.04:395][512]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.58.04:395][512]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1 Validando ativo
[2025.08.28-01.58.16:421][670]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.16:421][670]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.16:421][670]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.58.16:523][670]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.16:523][670]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.16:523][670]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Mid_T1", "tower_type": "basic", "location": {"x": 4000.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
[2025.08.28-01.58.16:523][670]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.58.16:740][670]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.58.16:740][670]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.58.16:740][670]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.58.16:761][670]LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
[2025.08.28-01.58.16:763][670]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Mid_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
[2025.08.28-01.58.16:763][670]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Mid_T1 spawned in world at location (4000.0, 0.0, 200.0)
[2025.08.28-01.58.16:763][670]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.58.16:808][670]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1] ([2] browsable assets)...
[2025.08.28-01.58.16:809][670]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.08.28-01.58.16:840][670]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.BP_Torre_Vermelha_Mid_T1]
[2025.08.28-01.58.16:840][670]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1]
[2025.08.28-01.58.16:840][670]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.uasset" SILENT=true
[2025.08.28-01.58.16:850][670]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
[2025.08.28-01.58.16:850][670]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Mid_T11EA7687B4C3822F0258463BF08C5E295.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.uasset'
[2025.08.28-01.58.16:868][670]LogFileHelpers: InternalPromptForCheckoutAndSave took 105.041 ms (total: 866.280 ms)
[2025.08.28-01.58.16:868][670]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Mid_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
[2025.08.28-01.58.16:868][670]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Mid_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.58.16:868][670]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Mid_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.58.16",
		"location":
		{
			"x": 4000,
			"y": 0,
			"z": 200
		}
	}
}
[2025.08.28-01.58.16:868][670]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
[2025.08.28-01.58.16:868][670]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.58.16:879][671]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.58.17:108][671]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.58.17:108][671]LogContentValidation: Enabled validators:
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.58.17:108][671]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.58.17:108][671]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1 Validando ativo
[2025.08.28-01.58.43:576][751]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.43:576][751]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.43:576][751]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.58.43:678][751]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.58.43:678][751]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.58.43:678][751]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Bot_T1", "tower_type": "basic", "location": {"x": 5000.0, "y": 5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
[2025.08.28-01.58.43:678][751]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-01.58.43:745][751]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-01.58.43:745][751]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.58.43:745][751]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.58.43:772][751]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.08.28-01.58.43:775][751]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Bot_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
[2025.08.28-01.58.43:776][751]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Bot_T1 spawned in world at location (5000.0, 5000.0, 200.0)
[2025.08.28-01.58.43:776][751]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-01.58.43:834][751]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1] ([2] browsable assets)...
[2025.08.28-01.58.43:835][751]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.08.28-01.58.43:877][751]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.BP_Torre_Vermelha_Bot_T1]
[2025.08.28-01.58.43:877][751]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1]
[2025.08.28-01.58.43:877][751]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.uasset" SILENT=true
[2025.08.28-01.58.43:888][751]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
[2025.08.28-01.58.43:888][751]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Bot_T11AAABB214DF1028AC0274D85DEF2C7E3.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.uasset'
[2025.08.28-01.58.43:901][751]LogFileHelpers: InternalPromptForCheckoutAndSave took 124.937 ms (total: 991.217 ms)
[2025.08.28-01.58.43:901][751]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Bot_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
[2025.08.28-01.58.43:901][751]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Bot_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-01.58.43:901][751]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Bot_T1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-22.58.43",
		"location":
		{
			"x": 5000,
			"y": 5000,
			"z": 200
		}
	}
}
[2025.08.28-01.58.43:901][751]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
[2025.08.28-01.58.43:901][751]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.58.43:913][752]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-01.58.44:131][752]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-01.58.44:131][752]LogContentValidation: Enabled validators:
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-01.58.44:131][752]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-01.58.44:131][752]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1 Validando ativo
[2025.08.28-01.59.18:123][223]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.18:123][223]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.18:123][223]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.18:225][223]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.18:225][223]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.18:225][223]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_nexus_architecture", "params": {"nexus_name": "Nexus_Azul_PlanicieRadiante", "location": {"x": -8000.0, "y": 0.0, "z": 300.0}, "team_side": "blue", "nexus_config": {"nexus_size": "large", "defensive_systems": ["crystal_barriers", "energy_dome", "auto_repair"], "energy_effects": ["light_pillar", "crystal_resonance", "healing_aura"], "architectural_grandeur": "high"}}}
[2025.08.28-01.59.18:225][223]LogTemp: Display: UnrealMCPBridge: Executing command: create_nexus_architecture
[2025.08.28-01.59.18:382][223]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_nexus_architecture
[2025.08.28-01.59.18:382][223]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.59.18:382][223]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.59.18:382][223]LogJson: Warning: Field complexity was not found.
[2025.08.28-01.59.18:382][223]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.59.18:396][223]LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Nexus_Azul_PlanicieRadiante with 4 nodes
[2025.08.28-01.59.18:400][223]LogTemp: AURACRON: Successfully created PCG component for structure Nexus_Azul_PlanicieRadiante (Layer: 0) with graph Auracron_Nexus_Azul_PlanicieRadiante_Layer0_PCG
[2025.08.28-01.59.18:400][223]LogTemp: SetupPCGGeneration: Created PCG component for structure Nexus_Azul_PlanicieRadiante (Layer: 0)
[2025.08.28-01.59.18:400][223]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.28-01.59.18:524][223]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.125 s
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: === Handled ensure: ===
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: 
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: Ensure condition failed: MyOwnerWorld  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\Components\ActorComponent.cpp] [Line: 1965] 
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: 
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: Stack: 
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff4c909369 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ffedaf99720 UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::CreateRobustTowerStructure() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:670]
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ffedafa05ba UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCreateNexusArchitecture() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:375]
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ffedaf9b348 UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCommand() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:90]
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ffedaf7202d UnrealEditor-UnrealMCP.dll!`UUnrealMCPBridge::ExecuteCommand'::`2'::<lambda_1>::operator()() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp:348]
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff5163cd83 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff5165fb72 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff5165284f UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff51652ece UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff51c41aa4 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff51c435bf UnrealEditor-Core.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ffedd2e0b86 UnrealEditor-MassEntityEditor.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff484237cb UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff4e272c25 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff489d1ff7 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fff496a5956 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70be99ce4 UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70bebe5ac UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70bebe6ba UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70bec209e UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70bed4e44 UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007ff70bed80fa UnrealEditor.exe!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fffec69e8d7 KERNEL32.DLL!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: [Callstack] 0x00007fffee5bc34c ntdll.dll!UnknownFunction []
[2025.08.28-01.59.18:524][223]LogOutputDevice: Error: 
[2025.08.28-01.59.18:529][223]LogStats:                SubmitErrorReport -  0.000 s
[2025.08.28-01.59.19:388][223]LogStats:                    SendNewReport -  0.859 s
[2025.08.28-01.59.19:388][223]LogStats:             FDebug::EnsureFailed -  0.988 s
[2025.08.28-01.59.19:388][223]LogTemp: CreateRobustTowerStructure: Created tower Nexus_Azul_PlanicieRadiante with 10 levels (Height: 2000.0, HISM: Yes, PCG: Yes)
[2025.08.28-01.59.19:388][223]LogTemp: HandleCreateNexusArchitecture: Created nexus Nexus_Azul_PlanicieRadiante for team 0 (Complexity: 5, Height: 2000.0)
[2025.08.28-01.59.19:388][223]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_nexus_architecture",
		"nexus_name": "Nexus_Azul_PlanicieRadiante",
		"team_index": 0,
		"complexity": 5,
		"nexus_height": 2000,
		"nexus_radius": 550,
		"nexus_levels": 10,
		"success": true,
		"timestamp": "2025.08.27-22.59.19"
	}
}
[2025.08.28-01.59.19:388][223]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 308
[2025.08.28-01.59.19:388][223]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.59.26:130][366]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.26:131][366]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.26:131][366]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.26:231][368]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.26:231][368]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.26:232][368]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_nexus_architecture", "params": {"nexus_name": "Nexus_Vermelho_PlanicieRadiante", "location": {"x": 8000.0, "y": 0.0, "z": 300.0}, "team_side": "red", "nexus_config": {"nexus_size": "large", "defensive_systems": ["shadow_barriers", "dark_dome", "auto_repair"], "energy_effects": ["dark_pillar", "shadow_resonance", "corruption_aura"], "architectural_grandeur": "high"}}}
[2025.08.28-01.59.26:232][368]LogTemp: Display: UnrealMCPBridge: Executing command: create_nexus_architecture
[2025.08.28-01.59.26:232][368]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_nexus_architecture
[2025.08.28-01.59.26:232][368]LogJson: Warning: Field team_index was not found.
[2025.08.28-01.59.26:232][368]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.59.26:232][368]LogJson: Warning: Field complexity was not found.
[2025.08.28-01.59.26:232][368]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-01.59.26:235][368]LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Nexus_Vermelho_PlanicieRadiante with 4 nodes
[2025.08.28-01.59.26:235][368]LogTemp: AURACRON: Successfully created PCG component for structure Nexus_Vermelho_PlanicieRadiante (Layer: 0) with graph Auracron_Nexus_Vermelho_PlanicieRadiante_Layer0_PCG
[2025.08.28-01.59.26:235][368]LogTemp: SetupPCGGeneration: Created PCG component for structure Nexus_Vermelho_PlanicieRadiante (Layer: 0)
[2025.08.28-01.59.26:235][368]LogTemp: CreateRobustTowerStructure: Created tower Nexus_Vermelho_PlanicieRadiante with 10 levels (Height: 2000.0, HISM: Yes, PCG: Yes)
[2025.08.28-01.59.26:235][368]LogTemp: HandleCreateNexusArchitecture: Created nexus Nexus_Vermelho_PlanicieRadiante for team 0 (Complexity: 5, Height: 2000.0)
[2025.08.28-01.59.26:235][368]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_nexus_architecture",
		"nexus_name": "Nexus_Vermelho_PlanicieRadiante",
		"team_index": 0,
		"complexity": 5,
		"nexus_height": 2000,
		"nexus_radius": 550,
		"nexus_levels": 10,
		"success": true,
		"timestamp": "2025.08.27-22.59.26"
	}
}
[2025.08.28-01.59.26:236][368]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 312
[2025.08.28-01.59.26:236][368]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.59.34:886][574]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.34:886][574]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.34:886][574]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.34:987][576]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.34:987][576]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.34:988][576]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Pequeno_1", "camp_type": "small", "location": {"x": -3000.0, "y": 2000.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "light_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "light_blessing"}, "respawn_timer": 60}}}
[2025.08.28-01.59.34:988][576]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-01.59.34:988][576]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-01.59.34:989][576]LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Pequeno_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-01.59.34:990][576]LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Pequeno_1 (Type: small, Layer: 0)
[2025.08.28-01.59.34:990][576]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Azul_Pequeno_1",
		"camp_type": "small",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-22.59.34"
	}
}
[2025.08.28-01.59.34:990][576]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 227
[2025.08.28-01.59.34:990][576]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.59.41:629][685]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.41:629][685]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.41:629][685]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.41:730][687]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.41:730][687]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.41:730][687]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Medio_1", "camp_type": "medium", "location": {"x": -2500.0, "y": 3500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "crystal_golem", "count": 1, "level": 3}, {"type": "light_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "crystal_armor"}, "respawn_timer": 90}}}
[2025.08.28-01.59.41:730][687]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-01.59.41:731][687]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-01.59.41:731][687]LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Medio_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-01.59.41:731][687]LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Medio_1 (Type: medium, Layer: 0)
[2025.08.28-01.59.41:731][687]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Azul_Medio_1",
		"camp_type": "medium",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-22.59.41"
	}
}
[2025.08.28-01.59.41:731][687]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
[2025.08.28-01.59.41:731][687]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.59.48:876][773]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.48:876][773]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.48:876][773]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.48:977][773]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.48:977][773]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.48:977][773]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Luz_Azul", "camp_type": "large", "location": {"x": -1500.0, "y": 1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "guardian_of_light", "count": 1, "level": 5}, {"type": "light_sprite", "count": 3, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_da_luz"}, "respawn_timer": 300}}}
[2025.08.28-01.59.48:977][773]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-01.59.49:114][773]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-01.59.49:114][773]LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Luz_Azul with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-01.59.49:114][773]LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Luz_Azul (Type: large, Layer: 0)
[2025.08.28-01.59.49:114][773]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Buff_Luz_Azul",
		"camp_type": "large",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-22.59.49"
	}
}
[2025.08.28-01.59.49:114][773]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
[2025.08.28-01.59.49:114][773]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-01.59.55:366][792]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.55:366][792]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.55:366][792]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-01.59.55:467][793]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-01.59.55:467][793]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-01.59.55:467][793]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Crescimento_Azul", "camp_type": "large", "location": {"x": -1500.0, "y": -1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "ancient_treant", "count": 1, "level": 5}, {"type": "nature_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_do_crescimento"}, "respawn_timer": 300}}}
[2025.08.28-01.59.55:467][793]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-01.59.55:472][793]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-01.59.55:472][793]LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Crescimento_Azul with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-01.59.55:472][793]LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Crescimento_Azul (Type: large, Layer: 0)
[2025.08.28-01.59.55:472][793]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Buff_Crescimento_Azul",
		"camp_type": "large",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-22.59.55"
	}
}
[2025.08.28-01.59.55:472][794]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 234
[2025.08.28-01.59.55:472][794]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.02:614][945]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.02:614][945]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.02:614][945]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.02:714][947]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.02:714][947]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.02:715][947]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Luz_Vermelho", "camp_type": "large", "location": {"x": 1500.0, "y": -1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "guardian_of_light", "count": 1, "level": 5}, {"type": "light_sprite", "count": 3, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_da_luz"}, "respawn_timer": 300}}}
[2025.08.28-02.00.02:715][947]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.00.02:716][947]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.00.02:717][947]LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Luz_Vermelho with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.00.02:717][947]LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Luz_Vermelho (Type: large, Layer: 0)
[2025.08.28-02.00.02:717][947]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Buff_Luz_Vermelho",
		"camp_type": "large",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.00.02"
	}
}
[2025.08.28-02.00.02:718][947]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
[2025.08.28-02.00.02:718][947]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.08:147][ 38]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.08:147][ 38]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.08:147][ 38]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.08:248][ 39]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.08:248][ 39]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.08:248][ 39]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Crescimento_Vermelho", "camp_type": "large", "location": {"x": 1500.0, "y": 1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "ancient_treant", "count": 1, "level": 5}, {"type": "nature_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_do_crescimento"}, "respawn_timer": 300}}}
[2025.08.28-02.00.08:248][ 39]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.00.08:488][ 39]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.00.08:488][ 39]LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Crescimento_Vermelho with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.00.08:488][ 39]LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Crescimento_Vermelho (Type: large, Layer: 0)
[2025.08.28-02.00.08:488][ 39]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Buff_Crescimento_Vermelho",
		"camp_type": "large",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.00.08"
	}
}
[2025.08.28-02.00.08:488][ 39]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 238
[2025.08.28-02.00.08:488][ 39]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.17:554][ 67]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.17:554][ 67]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.17:554][ 67]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.17:656][ 67]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.17:656][ 67]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.17:656][ 67]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Guardiao_da_Aurora", "camp_type": "epic", "location": {"x": 0.0, "y": 4000.0, "z": 200.0}, "layer_index": 0, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "aurora_guardian", "count": 1, "level": 12}], "reward_systems": {"gold": 1500, "experience": 2000, "buff": "aurora_blessing", "team_buff": true}, "respawn_timer": 420}}}
[2025.08.28-02.00.17:656][ 67]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.00.17:826][ 67]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.00.17:828][ 67]LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Guardiao_da_Aurora with 4 nodes
[2025.08.28-02.00.17:828][ 67]LogTemp: AURACRON: Successfully created PCG component for structure Guardiao_da_Aurora (Layer: 0) with graph Auracron_Guardiao_da_Aurora_Layer0_PCG
[2025.08.28-02.00.17:828][ 67]LogTemp: SetupPCGGeneration: Created PCG component for structure Guardiao_da_Aurora (Layer: 0)
[2025.08.28-02.00.17:828][ 67]LogTemp: CreateRobustTowerStructure: Created tower Guardiao_da_Aurora with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
[2025.08.28-02.00.17:828][ 67]LogTemp: HandleCreateJungleCamps: Created camp Guardiao_da_Aurora (Type: epic, Layer: 0)
[2025.08.28-02.00.17:828][ 67]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Guardiao_da_Aurora",
		"camp_type": "epic",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.00.17"
	}
}
[2025.08.28-02.00.17:828][ 67]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 225
[2025.08.28-02.00.17:828][ 67]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.27:083][217]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.27:083][217]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.27:083][217]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.27:185][217]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.27:185][217]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.27:185][217]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_portal_geometry", "params": {"portal_name": "Portal_Top_River", "portal_type": "teleporter", "source_location": {"x": 0.0, "y": 6000.0, "z": 1000.0}, "target_location": {"x": 0.0, "y": 6000.0, "z": 3000.0}, "portal_settings": {"portal_size": 400, "visual_effects": ["energy_spiral", "light_particles", "dimensional_rift"], "activation_method": "proximity", "transition_time": 3}}}
[2025.08.28-02.00.27:185][217]LogTemp: Display: UnrealMCPBridge: Executing command: create_portal_geometry
[2025.08.28-02.00.27:195][217]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_portal_geometry
[2025.08.28-02.00.27:196][217]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameters: portal_name, source_layer, target_layer"
}
[2025.08.28-02.00.27:196][217]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 107
[2025.08.28-02.00.27:196][217]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.41:294][409]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.41:294][409]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.41:294][409]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.41:395][409]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.41:395][409]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.41:395][409]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_portal_system", "params": {"map_name": "AuracronMainMap", "portals": [{"name": "Portal_Top_River", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": 6000, "z": 1000}, "target_location": {"x": 0, "y": 6000, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Mid_Center", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": 0, "z": 1000}, "target_location": {"x": 0, "y": 0, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Bot_River", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": -6000, "z": 1000}, "target_location": {"x": 0, "y": -6000, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_NE", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 4500, "y": 4500, "z": 1000}, "target_location": {"x": 4500, "y": 4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_NW", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": -4500, "y": 4500, "z": 1000}, "target_location": {"x": -4500, "y": 4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_SE", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 4500, "y": -4500, "z": 1000}, "target_location": {"x": 4500, "y": -4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}]}}
[2025.08.28-02.00.41:396][409]LogTemp: Display: UnrealMCPBridge: Executing command: create_portal_system
[2025.08.28-02.00.41:521][409]LogTemp: [MapSystem] Creating portal system...
[2025.08.28-02.00.41:522][409]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Map not found: /Game/Maps/AuracronMainMap"
}
[2025.08.28-02.00.41:522][409]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 80
[2025.08.28-02.00.41:522][409]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.47:652][522]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.47:652][522]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.47:652][522]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.47:753][524]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.47:753][524]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.47:753][524]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Top_River", "type": "STATICMESHACTOR", "location": [0.0, 6000.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.00.47:753][524]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.00.47:768][524]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.00.47:769][524]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Top_River",
		"class": "StaticMeshActor",
		"location": [ 0, 6000, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.00.47:769][524]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 195
[2025.08.28-02.00.47:769][524]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.53:424][542]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.53:424][542]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.53:424][542]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.53:526][542]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.53:526][542]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.53:526][542]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Mid_Center", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.00.53:526][542]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.00.53:752][542]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.00.53:752][542]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Mid_Center",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.00.53:752][542]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 193
[2025.08.28-02.00.53:752][542]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.00.58:384][615]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.58:384][615]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.58:384][615]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.00.58:486][617]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.00.58:486][617]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.00.58:486][617]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Bot_River", "type": "STATICMESHACTOR", "location": [0.0, -6000.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.00.58:486][617]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.00.58:487][617]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.00.58:488][617]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Bot_River",
		"class": "StaticMeshActor",
		"location": [ 0, -6000, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.00.58:488][617]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 196
[2025.08.28-02.00.58:488][617]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.03:212][727]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.03:212][727]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.03:212][727]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.03:313][729]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.03:313][729]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.03:313][729]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_NE", "type": "STATICMESHACTOR", "location": [4500.0, 4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.03:313][729]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.03:315][729]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.03:315][729]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Jungle_NE",
		"class": "StaticMeshActor",
		"location": [ 4500, 4500, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.03:316][729]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 198
[2025.08.28-02.01.03:316][729]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.07:938][820]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.07:938][820]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.07:938][820]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.08:039][821]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.08:039][821]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.08:039][821]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_NW", "type": "STATICMESHACTOR", "location": [-4500.0, 4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.08:039][821]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.08:040][821]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.08:040][821]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Jungle_NW",
		"class": "StaticMeshActor",
		"location": [ -4500, 4500, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.08:041][821]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 199
[2025.08.28-02.01.08:041][821]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.17:698][ 23]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.17:698][ 23]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.17:698][ 23]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.17:799][ 24]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.17:799][ 24]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.17:799][ 24]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_SE", "type": "STATICMESHACTOR", "location": [4500.0, -4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.17:799][ 24]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.17:800][ 24]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.17:800][ 24]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Portal_Jungle_SE",
		"class": "StaticMeshActor",
		"location": [ 4500, -4500, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.17:800][ 24]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 199
[2025.08.28-02.01.17:800][ 24]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.24:039][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.24:039][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.24:039][117]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.24:140][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.24:140][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.24:140][117]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_elevator_system", "params": {"map_name": "AuracronMainMap", "elevator_locations": [{"x": 0.0, "y": 7500.0, "z": 0.0}, {"x": 0.0, "y": -7500.0, "z": 0.0}, {"x": 7500.0, "y": 0.0, "z": 0.0}, {"x": -7500.0, "y": 0.0, "z": 0.0}], "capacity": 5, "travel_time": 2.5, "vulnerability_enabled": true}}
[2025.08.28-02.01.24:140][117]LogTemp: Display: UnrealMCPBridge: Executing command: create_elevator_system
[2025.08.28-02.01.24:183][117]LogTemp: [MapSystem] Creating elevator system...
[2025.08.28-02.01.24:183][117]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Map not found: /Game/Maps/AuracronMainMap"
}
[2025.08.28-02.01.24:183][117]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 80
[2025.08.28-02.01.24:183][117]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.29:424][133]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.29:424][133]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.29:424][133]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.29:525][134]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.29:525][134]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.29:525][134]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Norte", "type": "STATICMESHACTOR", "location": [0.0, 7500.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.29:525][134]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.29:849][134]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.29:849][134]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Elevador_Norte",
		"class": "StaticMeshActor",
		"location": [ 0, 7500, 0 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.29:849][134]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
[2025.08.28-02.01.29:849][134]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.34:394][148]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.34:394][148]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.34:394][148]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.34:495][148]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.34:496][148]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.34:496][148]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Sul", "type": "STATICMESHACTOR", "location": [0.0, -7500.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.34:496][148]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.34:521][148]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.34:521][148]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Elevador_Sul",
		"class": "StaticMeshActor",
		"location": [ 0, -7500, 0 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.34:521][148]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 189
[2025.08.28-02.01.34:521][148]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.38:156][159]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.38:156][159]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.38:156][159]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.38:257][160]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.38:257][160]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.38:257][160]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Leste", "type": "STATICMESHACTOR", "location": [7500.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.38:257][160]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.38:520][160]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.38:522][160]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Elevador_Leste",
		"class": "StaticMeshActor",
		"location": [ 7500, 0, 0 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.38:522][160]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
[2025.08.28-02.01.38:522][160]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.42:968][174]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.42:968][174]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.42:968][174]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.43:069][174]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.43:069][174]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.43:069][174]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Oeste", "type": "STATICMESHACTOR", "location": [-7500.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.28-02.01.43:069][174]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.01.43:190][174]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.01.43:191][174]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Elevador_Oeste",
		"class": "StaticMeshActor",
		"location": [ -7500, 0, 0 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.01.43:191][174]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 191
[2025.08.28-02.01.43:191][174]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.51:443][334]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.51:443][334]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.51:443][334]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.51:544][336]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.51:544][336]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.51:544][336]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "Corrente_Vento_Norte", "lane_type": "top", "start_location": {"x": -6000.0, "y": 4000.0, "z": 2100.0}, "end_location": {"x": 6000.0, "y": 4000.0, "z": 2100.0}, "lane_width": 1000.0, "geometry_settings": {"segments": 15, "elevation_curve": "floating", "side_barriers": false, "material_zones": ["wind", "cloud", "ethereal"]}}}
[2025.08.28-02.01.51:544][336]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-02.01.51:545][336]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-02.01.51:545][336]LogJson: Warning: Field layer_index was not found.
[2025.08.28-02.01.51:545][336]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.01.51:545][336]LogTemp: GenerateLaneGeometryWithFlow: Generated lane Corrente_Vento_Norte with 45 vertices, 64 triangles
[2025.08.28-02.01.51:546][336]LogTemp: CreateRobustProceduralMesh: Created mesh Corrente_Vento_Norte with 56 vertices
[2025.08.28-02.01.51:546][336]LogTemp: HandleCreateLaneGeometry: Created lane Corrente_Vento_Norte for layer 0 (Width: 1000.0, Points: 5)
[2025.08.28-02.01.51:546][336]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "Corrente_Vento_Norte",
		"lane_width": 1000,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-23.01.51"
	}
}
[2025.08.28-02.01.51:546][336]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 254
[2025.08.28-02.01.51:546][336]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.01.58:083][478]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.58:083][478]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.58:083][478]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.01.58:184][480]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.01.58:185][480]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.01.58:185][480]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "Corrente_Vento_Sul", "lane_type": "bottom", "start_location": {"x": -6000.0, "y": -4000.0, "z": 2100.0}, "end_location": {"x": 6000.0, "y": -4000.0, "z": 2100.0}, "lane_width": 1000.0, "geometry_settings": {"segments": 15, "elevation_curve": "floating", "side_barriers": false, "material_zones": ["wind", "cloud", "ethereal"]}}}
[2025.08.28-02.01.58:185][480]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-02.01.58:186][480]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-02.01.58:186][480]LogJson: Warning: Field layer_index was not found.
[2025.08.28-02.01.58:186][480]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.01.58:186][480]LogTemp: GenerateLaneGeometryWithFlow: Generated lane Corrente_Vento_Sul with 45 vertices, 64 triangles
[2025.08.28-02.01.58:187][480]LogTemp: CreateRobustProceduralMesh: Created mesh Corrente_Vento_Sul with 56 vertices
[2025.08.28-02.01.58:187][480]LogTemp: HandleCreateLaneGeometry: Created lane Corrente_Vento_Sul for layer 0 (Width: 1000.0, Points: 5)
[2025.08.28-02.01.58:187][480]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "Corrente_Vento_Sul",
		"lane_width": 1000,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-23.01.58"
	}
}
[2025.08.28-02.01.58:187][480]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 252
[2025.08.28-02.01.58:187][480]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.05:628][634]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.05:628][634]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.05:628][634]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.05:730][636]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.05:730][636]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.05:730][636]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Zephyr_Norte_1", "tower_type": "advanced", "location": {"x": -4000.0, "y": 4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "wind_spire", "defensive_features": ["wind_barrier", "aerial_targeting", "storm_shield"], "visual_effects": ["wind_swirl", "lightning_arcs", "cloud_formation"]}}}
[2025.08.28-02.02.05:730][636]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.02.05:731][636]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.02.05:731][636]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.02.05:731][636]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.05:777][636]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.08.28-02.02.05:783][636]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Zephyr_Norte_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
[2025.08.28-02.02.05:784][636]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Zephyr_Norte_1 spawned in world at location (-4000.0, 4000.0, 2200.0)
[2025.08.28-02.02.05:784][636]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.05:785][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:789][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:819][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:822][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:822][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:825][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:826][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:829][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:829][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:831][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:833][636]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1] ([2] browsable assets)...
[2025.08.28-02.02.05:834][636]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.08.28-02.02.05:836][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:837][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:880][636]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.BP_Torre_Azul_Zephyr_Norte_1]
[2025.08.28-02.02.05:880][636]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1]
[2025.08.28-02.02.05:880][636]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.uasset" SILENT=true
[2025.08.28-02.02.05:896][636]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
[2025.08.28-02.02.05:896][636]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Zephyr_Norte_170C4765843CC4DF0DDBB3D915DCE106B.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.uasset'
[2025.08.28-02.02.05:900][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:902][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:904][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:907][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:909][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:909][636]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.05:923][636]LogFileHelpers: InternalPromptForCheckoutAndSave took 139.807 ms (total: 1.13 sec)
[2025.08.28-02.02.05:924][636]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Zephyr_Norte_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
[2025.08.28-02.02.05:924][636]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Zephyr_Norte_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
[2025.08.28-02.02.05:924][636]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Zephyr_Norte_1",
		"tower_type": "advanced",
		"layer_index": 1,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.02.05",
		"location":
		{
			"x": -4000,
			"y": 4000,
			"z": 2200
		}
	}
}
[2025.08.28-02.02.05:924][636]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 466
[2025.08.28-02.02.05:924][636]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.05:926][636]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.02.05:993][637]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.02.05:993][637]LogContentValidation: Enabled validators:
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.02.05:993][637]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.02.05:993][637]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1 Validando ativo
[2025.08.28-02.02.08:986][731]LogUObjectHash: Compacting FUObjectHashTables data took   1.20ms
[2025.08.28-02.02.11:052][731]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.11:052][731]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.11:052][731]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.11:153][731]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.11:153][731]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.11:153][731]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Zephyr_Sul_1", "tower_type": "advanced", "location": {"x": -4000.0, "y": -4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "wind_spire", "defensive_features": ["wind_barrier", "aerial_targeting", "storm_shield"], "visual_effects": ["wind_swirl", "lightning_arcs", "cloud_formation"]}}}
[2025.08.28-02.02.11:153][731]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.02.11:163][731]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.02.11:163][731]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.02.11:163][731]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.11:185][731]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.28-02.02.11:188][731]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Zephyr_Sul_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
[2025.08.28-02.02.11:189][731]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Zephyr_Sul_1 spawned in world at location (-4000.0, -4000.0, 2200.0)
[2025.08.28-02.02.11:189][731]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.11:190][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:203][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:266][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:269][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:269][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:271][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:271][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:273][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:273][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:274][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:274][731]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1] ([2] browsable assets)...
[2025.08.28-02.02.11:275][731]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.08.28-02.02.11:276][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:277][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:310][731]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.BP_Torre_Azul_Zephyr_Sul_1]
[2025.08.28-02.02.11:310][731]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1]
[2025.08.28-02.02.11:310][731]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.uasset" SILENT=true
[2025.08.28-02.02.11:321][731]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
[2025.08.28-02.02.11:322][731]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Zephyr_Sul_199D032934C3D3FBC142B7D8A69E20057.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.uasset'
[2025.08.28-02.02.11:325][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:325][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:326][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:326][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:328][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:328][731]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.02.11:337][731]LogFileHelpers: InternalPromptForCheckoutAndSave took 148.098 ms (total: 1.27 sec)
[2025.08.28-02.02.11:337][731]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Zephyr_Sul_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
[2025.08.28-02.02.11:337][731]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Zephyr_Sul_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
[2025.08.28-02.02.11:337][731]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Zephyr_Sul_1",
		"tower_type": "advanced",
		"layer_index": 1,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.02.11",
		"location":
		{
			"x": -4000,
			"y": -4000,
			"z": 2200
		}
	}
}
[2025.08.28-02.02.11:338][731]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 465
[2025.08.28-02.02.11:338][731]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.12:252][731]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.08.28-02.02.12:287][731]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.12:346][731]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.28-02.02.12:383][731]LogUObjectHash: Compacting FUObjectHashTables data took   0.41ms
[2025.08.28-02.02.12:401][731]LogSavePackage: Moving output files for package: /Game/AURACRON
[2025.08.28-02.02.12:402][731]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON9861061C415371359E482C94211094D3.tmp' to '../../../../../../Game/AURACRON/Content/AURACRON.umap'
[2025.08.28-02.02.12:422][731]LogFileHelpers: Saving map 'AURACRON' took 0.076
[2025.08.28-02.02.12:437][731]LogFileHelpers: InternalPromptForCheckoutAndSave took 150.735 ms (total: 1.42 sec)
[2025.08.28-02.02.12:504][731]LogContentValidation: Display: Starting to validate 2 assets
[2025.08.28-02.02.12:504][731]LogContentValidation: Enabled validators:
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.02.12:504][731]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.02.12:504][731]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1 Validando ativo
[2025.08.28-02.02.12:504][731]AssetCheck: /Game/AURACRON Validando ativo
[2025.08.28-02.02.12:534][732]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.02.17:272][816]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.17:272][816]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.17:272][816]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.17:373][816]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.17:373][816]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.17:373][816]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Zephyr_Norte_1", "tower_type": "advanced", "location": {"x": 4000.0, "y": 4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "storm_spire", "defensive_features": ["dark_wind_barrier", "aerial_targeting", "shadow_storm"], "visual_effects": ["dark_wind_swirl", "red_lightning", "storm_clouds"]}}}
[2025.08.28-02.02.17:373][816]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.02.17:462][816]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.02.17:462][816]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.02.17:462][816]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.17:482][816]LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
[2025.08.28-02.02.17:485][816]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Zephyr_Norte_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
[2025.08.28-02.02.17:486][816]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Zephyr_Norte_1 spawned in world at location (4000.0, 4000.0, 2200.0)
[2025.08.28-02.02.17:486][816]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.17:545][816]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1] ([2] browsable assets)...
[2025.08.28-02.02.17:545][816]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.08.28-02.02.17:578][816]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1.BP_Torre_Vermelha_Zephyr_Norte_1]
[2025.08.28-02.02.17:578][816]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1]
[2025.08.28-02.02.17:589][816]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
[2025.08.28-02.02.17:590][816]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Zephyr_Norte_1FC2F20844C5B7CE0D0BC95943D7C8D2E.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1.uasset'
[2025.08.28-02.02.17:604][816]LogFileHelpers: InternalPromptForCheckoutAndSave took 118.355 ms (total: 1.54 sec)
[2025.08.28-02.02.17:604][816]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Zephyr_Norte_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
[2025.08.28-02.02.17:604][816]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Zephyr_Norte_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
[2025.08.28-02.02.17:605][816]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Zephyr_Norte_1",
		"tower_type": "advanced",
		"layer_index": 1,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.02.17",
		"location":
		{
			"x": 4000,
			"y": 4000,
			"z": 2200
		}
	}
}
[2025.08.28-02.02.17:605][816]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 469
[2025.08.28-02.02.17:605][816]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.17:613][817]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.02.17:843][817]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.02.17:843][817]LogContentValidation: Enabled validators:
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.02.17:843][817]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.02.17:843][817]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1 Validando ativo
[2025.08.28-02.02.22:947][833]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.22:947][833]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.22:947][833]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.23:048][833]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.23:048][833]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.23:048][833]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Zephyr_Sul_1", "tower_type": "advanced", "location": {"x": 4000.0, "y": -4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "storm_spire", "defensive_features": ["dark_wind_barrier", "aerial_targeting", "shadow_storm"], "visual_effects": ["dark_wind_swirl", "red_lightning", "storm_clouds"]}}}
[2025.08.28-02.02.23:048][833]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.02.23:130][833]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.02.23:130][833]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.02.23:130][833]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.23:151][833]LogUObjectHash: Compacting FUObjectHashTables data took   0.60ms
[2025.08.28-02.02.23:154][833]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Zephyr_Sul_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
[2025.08.28-02.02.23:155][833]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Zephyr_Sul_1 spawned in world at location (4000.0, -4000.0, 2200.0)
[2025.08.28-02.02.23:155][833]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.23:210][833]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1] ([2] browsable assets)...
[2025.08.28-02.02.23:211][833]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.08.28-02.02.23:249][833]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1.BP_Torre_Vermelha_Zephyr_Sul_1]
[2025.08.28-02.02.23:249][833]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1]
[2025.08.28-02.02.23:260][833]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
[2025.08.28-02.02.23:260][833]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Zephyr_Sul_1F638389B4C3FC6B7AADB9BA44D5BE1C2.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1.uasset'
[2025.08.28-02.02.23:275][833]LogFileHelpers: InternalPromptForCheckoutAndSave took 119.706 ms (total: 1.66 sec)
[2025.08.28-02.02.23:275][833]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Zephyr_Sul_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
[2025.08.28-02.02.23:275][833]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Zephyr_Sul_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
[2025.08.28-02.02.23:275][833]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Zephyr_Sul_1",
		"tower_type": "advanced",
		"layer_index": 1,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.02.23",
		"location":
		{
			"x": 4000,
			"y": -4000,
			"z": 2200
		}
	}
}
[2025.08.28-02.02.23:275][833]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 468
[2025.08.28-02.02.23:275][833]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.23:284][834]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.02.23:507][834]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.02.23:507][834]LogContentValidation: Enabled validators:
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.02.23:507][834]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.02.23:507][834]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1 Validando ativo
[2025.08.28-02.02.32:522][999]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.32:522][999]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.32:522][999]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.32:622][  1]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.32:623][  1]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.32:623][  1]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Senhor_dos_Ventos", "camp_type": "epic", "location": {"x": 0.0, "y": 0.0, "z": 2300.0}, "layer_index": 1, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "wind_lord", "count": 1, "level": 15}], "reward_systems": {"gold": 2000, "experience": 2500, "buff": "wind_mastery", "team_buff": true}, "respawn_timer": 480}}}
[2025.08.28-02.02.32:623][  1]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.02.32:624][  1]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.02.32:634][  1]LogPCG: Error: From node AttributeFilter_0 does not have the Out label
[2025.08.28-02.02.32:634][  1]LogTemp: AURACRON: FULLY CONFIGURED PCG for Firmamento Zephyr - Senhor_dos_Ventos with 5 nodes
[2025.08.28-02.02.32:634][  1]LogTemp: AURACRON: Successfully created PCG component for structure Senhor_dos_Ventos (Layer: 1) with graph Auracron_Senhor_dos_Ventos_Layer1_PCG
[2025.08.28-02.02.32:634][  1]LogTemp: SetupPCGGeneration: Created PCG component for structure Senhor_dos_Ventos (Layer: 1)
[2025.08.28-02.02.32:634][  1]LogTemp: CreateRobustTowerStructure: Created tower Senhor_dos_Ventos with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
[2025.08.28-02.02.32:634][  1]LogTemp: HandleCreateJungleCamps: Created camp Senhor_dos_Ventos (Type: epic, Layer: 1)
[2025.08.28-02.02.32:634][  1]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Senhor_dos_Ventos",
		"camp_type": "epic",
		"layer_index": 1,
		"success": true,
		"timestamp": "2025.08.27-23.02.32"
	}
}
[2025.08.28-02.02.32:635][  1]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 224
[2025.08.28-02.02.32:635][  1]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.41:195][ 86]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.41:195][ 86]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.41:195][ 86]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.41:296][ 86]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.41:296][ 86]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.41:296][ 86]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Azul_1", "camp_type": "medium", "location": {"x": -3000.0, "y": 2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "storm_elemental", "count": 1, "level": 4}, {"type": "wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "storm_speed"}, "respawn_timer": 90}}}
[2025.08.28-02.02.41:296][ 86]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.02.41:458][ 86]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.02.41:458][ 86]LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.02.41:458][ 86]LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Azul_1 (Type: medium, Layer: 1)
[2025.08.28-02.02.41:458][ 86]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Tempestade_Azul_1",
		"camp_type": "medium",
		"layer_index": 1,
		"success": true,
		"timestamp": "2025.08.27-23.02.41"
	}
}
[2025.08.28-02.02.41:458][ 86]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
[2025.08.28-02.02.41:458][ 86]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.48:803][109]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.48:803][109]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.48:803][109]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.48:904][109]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.48:904][109]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.48:904][109]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_structures", "params": {"structure_name": "Labirinto_Umbral_Centro", "structure_type": "monster_den", "location": {"x": 0.0, "y": 0.0, "z": 4200.0}, "layer_index": 2, "structure_settings": {"size_variation": "large", "detail_level": "high", "vegetation": false, "defensive_elements": ["shadow_walls", "umbral_traps", "stealth_zones"]}}}
[2025.08.28-02.02.48:904][109]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_structures
[2025.08.28-02.02.49:126][109]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_jungle_structures
[2025.08.28-02.02.49:126][109]LogJson: Warning: Field complexity was not found.
[2025.08.28-02.02.49:126][109]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.49:127][109]LogTemp: CreateRobustProceduralMesh: Created mesh JungleStructure_monster_den_L2 with 56 vertices
[2025.08.28-02.02.49:127][109]LogTemp: HandleCreateJungleStructures: Created structure monster_den at layer 2 (Complexity: 3)
[2025.08.28-02.02.49:128][109]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_structures",
		"structure_type": "monster_den",
		"layer_index": 2,
		"complexity": 3,
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 4200
		},
		"success": true,
		"timestamp": "2025.08.27-23.02.49"
	}
}
[2025.08.28-02.02.49:128][109]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 288
[2025.08.28-02.02.49:128][109]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.55:672][129]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.55:673][129]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.55:673][129]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.02.55:775][129]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.02.55:775][129]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.02.55:775][129]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Umbral_1", "tower_type": "nexus", "location": {"x": -3000.0, "y": 3000.0, "z": 4300.0}, "layer_index": 2, "tower_config": {"height": 1200, "base_radius": 400, "architectural_style": "shadow_obelisk", "defensive_features": ["umbral_shield", "stealth_detection", "shadow_strike"], "visual_effects": ["dark_aura", "shadow_tendrils", "void_energy"]}}}
[2025.08.28-02.02.55:775][129]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.02.55:793][129]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.02.55:793][129]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.02.55:793][129]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.02.55:815][129]LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
[2025.08.28-02.02.55:819][129]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Umbral_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
[2025.08.28-02.02.55:820][129]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Umbral_1 spawned in world at location (-3000.0, 3000.0, 4300.0)
[2025.08.28-02.02.55:820][129]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.02.55:870][129]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1] ([2] browsable assets)...
[2025.08.28-02.02.55:870][129]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.08.28-02.02.55:915][129]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.BP_Torre_Azul_Umbral_1]
[2025.08.28-02.02.55:916][129]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1]
[2025.08.28-02.02.55:916][129]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.uasset" SILENT=true
[2025.08.28-02.02.55:928][129]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
[2025.08.28-02.02.55:928][129]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Umbral_1312717C048AA6B84A0BAA582DE1B47FE.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.uasset'
[2025.08.28-02.02.55:947][129]LogFileHelpers: InternalPromptForCheckoutAndSave took 126.372 ms (total: 1.79 sec)
[2025.08.28-02.02.55:947][129]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Umbral_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
[2025.08.28-02.02.55:947][129]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Umbral_1 (Type: nexus, Layer: 2, Team: 0, Height: 1200.0)
[2025.08.28-02.02.55:947][129]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Umbral_1",
		"tower_type": "nexus",
		"layer_index": 2,
		"team_index": 0,
		"tower_height": 1200,
		"tower_radius": 200,
		"tower_levels": 7,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.02.55",
		"location":
		{
			"x": -3000,
			"y": 3000,
			"z": 4300
		}
	}
}
[2025.08.28-02.02.55:947][129]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
[2025.08.28-02.02.55:947][129]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.02.55:956][129]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.02.56:179][130]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.02.56:179][130]LogContentValidation: Enabled validators:
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.02.56:179][130]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.02.56:179][130]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1 Validando ativo
[2025.08.28-02.03.01:281][146]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.01:281][146]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.01:281][146]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.01:382][146]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.01:382][146]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.01:382][146]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Umbral_1", "tower_type": "nexus", "location": {"x": 3000.0, "y": -3000.0, "z": 4300.0}, "layer_index": 2, "tower_config": {"height": 1200, "base_radius": 400, "architectural_style": "void_obelisk", "defensive_features": ["void_shield", "stealth_detection", "corruption_strike"], "visual_effects": ["crimson_aura", "void_tendrils", "dark_energy"]}}}
[2025.08.28-02.03.01:382][146]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.03.01:461][146]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.03.01:461][146]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.03.01:461][146]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.03.01:482][146]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.08.28-02.03.01:485][146]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Umbral_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
[2025.08.28-02.03.01:486][146]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Umbral_1 spawned in world at location (3000.0, -3000.0, 4300.0)
[2025.08.28-02.03.01:486][146]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.01:529][146]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1] ([2] browsable assets)...
[2025.08.28-02.03.01:530][146]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.08.28-02.03.01:569][146]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.BP_Torre_Vermelha_Umbral_1]
[2025.08.28-02.03.01:569][146]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1]
[2025.08.28-02.03.01:569][146]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.uasset" SILENT=true
[2025.08.28-02.03.01:580][146]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
[2025.08.28-02.03.01:580][146]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Umbral_1D1AEE9DC442A7CF82BF2C699BF383B2A.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.uasset'
[2025.08.28-02.03.01:596][146]LogFileHelpers: InternalPromptForCheckoutAndSave took 109.677 ms (total: 1.90 sec)
[2025.08.28-02.03.01:596][146]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Umbral_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
[2025.08.28-02.03.01:596][146]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Umbral_1 (Type: nexus, Layer: 2, Team: 0, Height: 1200.0)
[2025.08.28-02.03.01:596][146]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Umbral_1",
		"tower_type": "nexus",
		"layer_index": 2,
		"team_index": 0,
		"tower_height": 1200,
		"tower_radius": 200,
		"tower_levels": 7,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.03.01",
		"location":
		{
			"x": 3000,
			"y": -3000,
			"z": 4300
		}
	}
}
[2025.08.28-02.03.01:596][146]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 462
[2025.08.28-02.03.01:596][146]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.01:604][146]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.03.01:829][147]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.03.01:829][147]LogContentValidation: Enabled validators:
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.03.01:829][147]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.03.01:829][147]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1 Validando ativo
[2025.08.28-02.03.08:745][252]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.08:745][252]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.08:745][252]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.08:847][254]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.08:847][254]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.08:847][254]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Arqui_Sombra", "camp_type": "epic", "location": {"x": 0.0, "y": 0.0, "z": 4400.0}, "layer_index": 2, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "arch_shadow", "count": 1, "level": 20}], "reward_systems": {"gold": 3000, "experience": 4000, "buff": "umbral_dominion", "team_buff": true}, "respawn_timer": 600}}}
[2025.08.28-02.03.08:847][254]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.03.08:848][254]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.03.08:849][254]LogPCG: Error: From node AttributeFilter_0 does not have the Out label
[2025.08.28-02.03.08:849][254]LogTemp: AURACRON: FULLY CONFIGURED PCG for Abismo Umbral - Arqui_Sombra with 5 nodes
[2025.08.28-02.03.08:849][254]LogTemp: AURACRON: Successfully created PCG component for structure Arqui_Sombra (Layer: 2) with graph Auracron_Arqui_Sombra_Layer2_PCG
[2025.08.28-02.03.08:849][254]LogTemp: SetupPCGGeneration: Created PCG component for structure Arqui_Sombra (Layer: 2)
[2025.08.28-02.03.08:849][254]LogTemp: CreateRobustTowerStructure: Created tower Arqui_Sombra with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
[2025.08.28-02.03.08:849][254]LogTemp: HandleCreateJungleCamps: Created camp Arqui_Sombra (Type: epic, Layer: 2)
[2025.08.28-02.03.08:849][254]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Arqui_Sombra",
		"camp_type": "epic",
		"layer_index": 2,
		"success": true,
		"timestamp": "2025.08.27-23.03.08"
	}
}
[2025.08.28-02.03.08:849][254]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 219
[2025.08.28-02.03.08:849][254]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.17:697][425]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.17:697][425]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.17:697][425]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.17:798][425]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.17:799][425]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.17:799][425]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_PlanicieRadiante_Terreno", "layer_index": 0, "material_type": "terrain", "material_properties": {"base_color": [0.4, 0.8, 0.3, 1], "metallic": 0.1, "roughness": 0.7, "normal_intensity": 1.2, "emissive_color": [0.2, 0.4, 0.1, 1], "opacity": 1}}}
[2025.08.28-02.03.17:799][425]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.28-02.03.17:870][425]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.28-02.03.17:881][425]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.17:939][425]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno] ([1] browsable assets)...
[2025.08.28-02.03.17:973][425]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.08.28-02.03.18:012][425]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.Material_PlanicieRadiante_Terreno]
[2025.08.28-02.03.18:012][425]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno]
[2025.08.28-02.03.18:023][425]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno
[2025.08.28-02.03.18:023][425]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_PlanicieRadiante_Terren8CD0F0B744EEE875FF8A7599869D01F2.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.uasset'
[2025.08.28-02.03.18:040][425]LogFileHelpers: InternalPromptForCheckoutAndSave took 158.687 ms (total: 2.06 sec)
[2025.08.28-02.03.18:040][425]LogTemp: CreateRobustMaterial: Successfully created and saved material Material_PlanicieRadiante_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno)
[2025.08.28-02.03.18:040][425]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.18:089][425]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance] ([1] browsable assets)...
[2025.08.28-02.03.18:112][425]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.Material_PlanicieRadiante_Terreno_Instance]
[2025.08.28-02.03.18:112][425]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance]
[2025.08.28-02.03.18:121][425]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance
[2025.08.28-02.03.18:121][425]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_PlanicieRadiante_Terren839CADB24656E4E2FB8F6DB1B9F671A4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.uasset'
[2025.08.28-02.03.18:136][425]LogFileHelpers: InternalPromptForCheckoutAndSave took 95.159 ms (total: 2.15 sec)
[2025.08.28-02.03.18:136][425]LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_PlanicieRadiante_Terreno_Instance (Layer: 0, Saved: Yes, Path: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance)
[2025.08.28-02.03.18:136][425]LogTemp: HandleCreateLayerMaterials: Created material Material_PlanicieRadiante_Terreno for layer 0 (Type: terrain, Nanite: Yes)
[2025.08.28-02.03.18:136][425]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_PlanicieRadiante_Terreno",
		"material_type": "terrain",
		"layer_index": 0,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"material_asset_path": "/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno",
		"instance_asset_path": "/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance",
		"material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.uasset",
		"instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.uasset",
		"material_saved_to_disk": true,
		"instance_saved_to_disk": true,
		"files_created": true,
		"timestamp": "2025.08.27-23.03.18",
		"color_scheme":
		{
			"primary_color": "(R=1.000000,G=0.800000,B=0.200000,A=1.000000)",
			"secondary_color": "(R=0.200000,G=0.800000,B=0.300000,A=1.000000)",
			"accent_color": "(R=1.000000,G=1.000000,B=0.800000,A=1.000000)"
		}
	}
}
[2025.08.28-02.03.18:136][425]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1151
[2025.08.28-02.03.18:136][425]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.18:145][426]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.03.18:258][426]LogContentValidation: Display: Starting to validate 2 assets
[2025.08.28-02.03.18:258][426]LogContentValidation: Enabled validators:
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.03.18:258][426]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.03.18:260][426]AssetCheck: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno Validando ativo
[2025.08.28-02.03.18:260][426]AssetCheck: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance Validando ativo
[2025.08.28-02.03.26:903][453]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.26:903][453]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.26:903][453]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.27:004][453]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.27:004][453]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.27:004][453]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_FirmamentoZephyr_Terreno", "layer_index": 1, "material_type": "terrain", "material_properties": {"base_color": [0.6, 0.8, 1, 1], "metallic": 0.3, "roughness": 0.4, "normal_intensity": 0.8, "emissive_color": [0.4, 0.6, 0.9, 1], "opacity": 0.8}}}
[2025.08.28-02.03.27:004][453]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.28-02.03.27:206][453]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.28-02.03.27:211][453]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.27:268][453]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno] ([1] browsable assets)...
[2025.08.28-02.03.27:320][453]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.Material_FirmamentoZephyr_Terreno]
[2025.08.28-02.03.27:320][453]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno]
[2025.08.28-02.03.27:330][453]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno
[2025.08.28-02.03.27:330][453]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_FirmamentoZephyr_Terren597303D4489CAA5967C7C3928E2847D5.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.uasset'
[2025.08.28-02.03.27:342][453]LogFileHelpers: InternalPromptForCheckoutAndSave took 132.348 ms (total: 2.29 sec)
[2025.08.28-02.03.27:342][453]LogTemp: CreateRobustMaterial: Successfully created and saved material Material_FirmamentoZephyr_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno)
[2025.08.28-02.03.27:344][453]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.27:407][453]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance] ([1] browsable assets)...
[2025.08.28-02.03.27:433][453]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.Material_FirmamentoZephyr_Terreno_Instance]
[2025.08.28-02.03.27:433][453]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance]
[2025.08.28-02.03.27:454][453]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance
[2025.08.28-02.03.27:454][453]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_FirmamentoZephyr_Terren9B0402834E3F6174A43094A7A06D1C6F.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.uasset'
[2025.08.28-02.03.27:469][453]LogFileHelpers: InternalPromptForCheckoutAndSave took 124.950 ms (total: 2.41 sec)
[2025.08.28-02.03.27:469][453]LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_FirmamentoZephyr_Terreno_Instance (Layer: 1, Saved: Yes, Path: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance)
[2025.08.28-02.03.27:469][453]LogTemp: HandleCreateLayerMaterials: Created material Material_FirmamentoZephyr_Terreno for layer 1 (Type: terrain, Nanite: Yes)
[2025.08.28-02.03.27:469][453]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_FirmamentoZephyr_Terreno",
		"material_type": "terrain",
		"layer_index": 1,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"material_asset_path": "/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno",
		"instance_asset_path": "/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance",
		"material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.uasset",
		"instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.uasset",
		"material_saved_to_disk": true,
		"instance_saved_to_disk": true,
		"files_created": true,
		"timestamp": "2025.08.27-23.03.27",
		"color_scheme":
		{
			"primary_color": "(R=0.200000,G=0.600000,B=1.000000,A=1.000000)",
			"secondary_color": "(R=0.900000,G=0.900000,B=1.000000,A=1.000000)",
			"accent_color": "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)"
		}
	}
}
[2025.08.28-02.03.27:469][453]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1151
[2025.08.28-02.03.27:469][453]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.27:477][454]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.03.27:585][454]LogContentValidation: Display: Starting to validate 2 assets
[2025.08.28-02.03.27:586][454]LogContentValidation: Enabled validators:
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.03.27:586][454]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.03.27:586][454]AssetCheck: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno Validando ativo
[2025.08.28-02.03.27:586][454]AssetCheck: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance Validando ativo
[2025.08.28-02.03.34:003][584]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.34:003][584]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.34:003][584]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.34:103][586]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.34:104][586]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.34:104][586]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_AbismoUmbral_Terreno", "layer_index": 2, "material_type": "terrain", "material_properties": {"base_color": [0.2, 0.1, 0.3, 1], "metallic": 0.8, "roughness": 0.3, "normal_intensity": 1.5, "emissive_color": [0.4, 0.1, 0.6, 1], "opacity": 1}}}
[2025.08.28-02.03.34:104][586]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.28-02.03.34:113][586]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.28-02.03.34:114][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:208][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:214][586]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.34:216][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:260][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:303][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:305][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:307][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:309][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:311][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:312][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:313][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:315][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:315][586]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno] ([1] browsable assets)...
[2025.08.28-02.03.34:371][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:407][586]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.Material_AbismoUmbral_Terreno]
[2025.08.28-02.03.34:407][586]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno]
[2025.08.28-02.03.34:419][586]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno
[2025.08.28-02.03.34:419][586]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_AbismoUmbral_TerrenoAFF425FE45C06BF1A8D515A86A7CAFA7.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.uasset'
[2025.08.28-02.03.34:432][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:435][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:435][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:437][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:438][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:439][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:453][586]LogFileHelpers: InternalPromptForCheckoutAndSave took 238.474 ms (total: 2.65 sec)
[2025.08.28-02.03.34:453][586]LogTemp: CreateRobustMaterial: Successfully created and saved material Material_AbismoUmbral_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno)
[2025.08.28-02.03.34:453][586]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.03.34:455][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:459][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:503][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:504][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:504][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:506][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:506][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:508][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:508][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:511][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:511][586]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance] ([1] browsable assets)...
[2025.08.28-02.03.34:511][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:525][586]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.Material_AbismoUmbral_Terreno_Instance]
[2025.08.28-02.03.34:525][586]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance]
[2025.08.28-02.03.34:538][586]LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance
[2025.08.28-02.03.34:538][586]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_AbismoUmbral_Terreno_InA302C9CC443CC4461EA6358C894BB6A4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.uasset'
[2025.08.28-02.03.34:549][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:551][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:551][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:553][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:554][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:554][586]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.03.34:568][586]LogFileHelpers: InternalPromptForCheckoutAndSave took 113.480 ms (total: 2.76 sec)
[2025.08.28-02.03.34:568][586]LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_AbismoUmbral_Terreno_Instance (Layer: 2, Saved: Yes, Path: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance)
[2025.08.28-02.03.34:568][586]LogTemp: HandleCreateLayerMaterials: Created material Material_AbismoUmbral_Terreno for layer 2 (Type: terrain, Nanite: Yes)
[2025.08.28-02.03.34:568][586]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_AbismoUmbral_Terreno",
		"material_type": "terrain",
		"layer_index": 2,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"material_asset_path": "/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno",
		"instance_asset_path": "/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance",
		"material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.uasset",
		"instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.uasset",
		"material_saved_to_disk": true,
		"instance_saved_to_disk": true,
		"files_created": true,
		"timestamp": "2025.08.27-23.03.34",
		"color_scheme":
		{
			"primary_color": "(R=0.400000,G=0.100000,B=0.800000,A=1.000000)",
			"secondary_color": "(R=0.100000,G=0.100000,B=0.200000,A=1.000000)",
			"accent_color": "(R=0.800000,G=0.200000,B=1.000000,A=1.000000)"
		}
	}
}
[2025.08.28-02.03.34:568][586]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1131
[2025.08.28-02.03.34:568][586]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.34:569][586]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.03.34:627][587]LogContentValidation: Display: Starting to validate 2 assets
[2025.08.28-02.03.34:627][587]LogContentValidation: Enabled validators:
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.03.34:627][587]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.03.34:627][587]AssetCheck: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno Validando ativo
[2025.08.28-02.03.34:627][587]AssetCheck: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance Validando ativo
[2025.08.28-02.03.42:726][756]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.42:726][756]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.42:726][756]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.42:840][757]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.42:840][757]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.42:840][757]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_PlanicieRadiante_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 1500.0}, "layer_index": 0, "intensity": 5, "color": {"r": 1, "g": 0.9, "b": 0.7, "a": 1}, "attenuation_radius": 20000, "cast_shadows": true, "use_lumen": true}}
[2025.08.28-02.03.42:840][757]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.28-02.03.43:085][757]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.28-02.03.43:088][757]LogTemp: CreateRobustLighting: Created light Luz_PlanicieRadiante_Principal (Type: directional, Intensity: 5.0, Lumen: Yes, Shadows: Yes)
[2025.08.28-02.03.43:088][757]LogTemp: HandleCreateDynamicLighting: Created light Luz_PlanicieRadiante_Principal (Type: directional, Layer: 0, Intensity: 5.0, Success: Yes)
[2025.08.28-02.03.43:089][757]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Luz_PlanicieRadiante_Principal",
		"light_type": "directional",
		"layer_index": 0,
		"intensity": 5,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-23.03.43",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 1500
		},
		"color":
		{
			"r": 1,
			"g": 0.89999997615814209,
			"b": 0.69999998807907104,
			"a": 1
		}
	}
}
[2025.08.28-02.03.43:089][757]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 486
[2025.08.28-02.03.43:089][757]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.49:516][831]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.49:516][831]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.49:516][831]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.49:617][833]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.49:617][833]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.49:617][833]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_FirmamentoZephyr_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 3500.0}, "layer_index": 1, "intensity": 4, "color": {"r": 0.7, "g": 0.9, "b": 1, "a": 1}, "attenuation_radius": 18000, "cast_shadows": true, "use_lumen": true}}
[2025.08.28-02.03.49:617][833]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.28-02.03.49:618][833]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.28-02.03.49:618][833]LogTemp: CreateRobustLighting: Created light Luz_FirmamentoZephyr_Principal (Type: directional, Intensity: 4.0, Lumen: Yes, Shadows: Yes)
[2025.08.28-02.03.49:618][833]LogTemp: HandleCreateDynamicLighting: Created light Luz_FirmamentoZephyr_Principal (Type: directional, Layer: 1, Intensity: 4.0, Success: Yes)
[2025.08.28-02.03.49:618][833]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Luz_FirmamentoZephyr_Principal",
		"light_type": "directional",
		"layer_index": 1,
		"intensity": 4,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-23.03.49",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 3500
		},
		"color":
		{
			"r": 0.69999998807907104,
			"g": 0.89999997615814209,
			"b": 1,
			"a": 1
		}
	}
}
[2025.08.28-02.03.49:618][833]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 486
[2025.08.28-02.03.49:618][833]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.03.49:741][834]LogSlate: Took 0.000223 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.28-02.03.55:352][948]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.55:353][948]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.55:353][948]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.03.55:454][954]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.03.55:454][954]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.03.55:454][954]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_AbismoUmbral_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 5500.0}, "layer_index": 2, "intensity": 2, "color": {"r": 0.4, "g": 0.2, "b": 0.8, "a": 1}, "attenuation_radius": 15000, "cast_shadows": true, "use_lumen": true}}
[2025.08.28-02.03.55:454][954]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.28-02.03.55:459][954]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.28-02.03.55:459][954]LogTemp: CreateRobustLighting: Created light Luz_AbismoUmbral_Principal (Type: directional, Intensity: 2.0, Lumen: Yes, Shadows: Yes)
[2025.08.28-02.03.55:459][954]LogTemp: HandleCreateDynamicLighting: Created light Luz_AbismoUmbral_Principal (Type: directional, Layer: 2, Intensity: 2.0, Success: Yes)
[2025.08.28-02.03.55:459][954]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Luz_AbismoUmbral_Principal",
		"light_type": "directional",
		"layer_index": 2,
		"intensity": 2,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-23.03.55",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 5500
		},
		"color":
		{
			"r": 0.40000000596046448,
			"g": 0.20000000298023224,
			"b": 0.80000001192092896,
			"a": 1
		}
	}
}
[2025.08.28-02.03.55:459][954]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 500
[2025.08.28-02.03.55:459][954]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.02:100][ 95]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.02:101][ 95]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.02:101][ 95]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.02:201][ 96]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.02:201][ 96]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.02:201][ 96]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_PlanicieRadiante_Cristais", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 1200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 2, "y": 2, "z": 2}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
[2025.08.28-02.04.02:201][ 96]LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
[2025.08.28-02.04.02:201][ 96]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
[2025.08.28-02.04.02:201][ 96]LogJson: Warning: Field niagara_system was not found.
[2025.08.28-02.04.02:201][ 96]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.02:201][ 96]LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
[2025.08.28-02.04.02:202][ 96]LogStreaming: Warning: LoadPackage: SkipPackage: /Engine/VFX/P_Environmental (0xEE3B0B0271E4E611) - The package to load does not exist on disk or in the loader
[2025.08.28-02.04.02:202][ 96]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
[2025.08.28-02.04.02:203][ 96]LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_PlanicieRadiante_Cristais using mesh /Engine/BasicShapes/Plane
[2025.08.28-02.04.02:203][ 96]LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_PlanicieRadiante_Cristais (Type: environmental, System: , Success: Yes)
[2025.08.28-02.04.02:203][ 96]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "setup_niagara_effects",
		"effect_name": "Efeito_PlanicieRadiante_Cristais",
		"effect_type": "environmental",
		"niagara_system": "",
		"auto_activate": true,
		"success": true,
		"timestamp": "2025.08.27-23.04.02"
	}
}
[2025.08.28-02.04.02:203][ 96]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 284
[2025.08.28-02.04.02:203][ 96]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.08:237][174]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.08:237][174]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.08:237][174]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.08:338][174]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.08:338][174]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.08:338][174]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_FirmamentoZephyr_Ventos", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 3200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 3, "y": 3, "z": 1.5}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
[2025.08.28-02.04.08:338][174]LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
[2025.08.28-02.04.08:551][174]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
[2025.08.28-02.04.08:551][174]LogJson: Warning: Field niagara_system was not found.
[2025.08.28-02.04.08:551][174]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.08:552][174]LogStreaming: Display: FlushAsyncLoading(479): 1 QueuedPackages, 0 AsyncPackages
[2025.08.28-02.04.08:552][174]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
[2025.08.28-02.04.08:552][174]LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_FirmamentoZephyr_Ventos using mesh /Engine/BasicShapes/Plane
[2025.08.28-02.04.08:552][174]LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_FirmamentoZephyr_Ventos (Type: environmental, System: , Success: Yes)
[2025.08.28-02.04.08:552][174]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "setup_niagara_effects",
		"effect_name": "Efeito_FirmamentoZephyr_Ventos",
		"effect_type": "environmental",
		"niagara_system": "",
		"auto_activate": true,
		"success": true,
		"timestamp": "2025.08.27-23.04.08"
	}
}
[2025.08.28-02.04.08:552][174]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 282
[2025.08.28-02.04.08:552][174]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.14:704][199]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.14:704][199]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.14:704][199]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.14:805][202]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.14:805][202]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.14:805][202]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_AbismoUmbral_Sombras", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 5200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 2.5, "y": 2.5, "z": 2}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
[2025.08.28-02.04.14:805][202]LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
[2025.08.28-02.04.14:806][202]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
[2025.08.28-02.04.14:806][202]LogJson: Warning: Field niagara_system was not found.
[2025.08.28-02.04.14:806][202]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.14:806][202]LogStreaming: Display: FlushAsyncLoading(480): 1 QueuedPackages, 0 AsyncPackages
[2025.08.28-02.04.14:806][202]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
[2025.08.28-02.04.14:806][202]LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_AbismoUmbral_Sombras using mesh /Engine/BasicShapes/Plane
[2025.08.28-02.04.14:807][202]LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_AbismoUmbral_Sombras (Type: environmental, System: , Success: Yes)
[2025.08.28-02.04.14:807][202]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "setup_niagara_effects",
		"effect_name": "Efeito_AbismoUmbral_Sombras",
		"effect_type": "environmental",
		"niagara_system": "",
		"auto_activate": true,
		"success": true,
		"timestamp": "2025.08.27-23.04.14"
	}
}
[2025.08.28-02.04.14:807][202]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 279
[2025.08.28-02.04.14:807][202]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.25:264][411]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.25:265][411]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.25:265][411]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.25:366][413]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.25:366][413]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.25:366][413]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_collision_profiles", "params": {"profile_system_name": "AuracronCollisionSystem", "layer_profiles": [{"layer_name": "PlanicieRadiante", "collision_enabled": "collision_enabled", "object_type": "RadianteLayer", "collision_responses": {"RadianteLayer": "block", "ZephyrLayer": "ignore", "UmbralLayer": "ignore", "VerticalConnector": "overlap"}}, {"layer_name": "FirmamentoZephyr", "collision_enabled": "collision_enabled", "object_type": "ZephyrLayer", "collision_responses": {"RadianteLayer": "ignore", "ZephyrLayer": "block", "UmbralLayer": "ignore", "VerticalConnector": "overlap"}}, {"layer_name": "AbismoUmbral", "collision_enabled": "collision_enabled", "object_type": "UmbralLayer", "collision_responses": {"RadianteLayer": "ignore", "ZephyrLayer": "ignore", "UmbralLayer": "block", "VerticalConnector": "overlap"}}], "collision_channels": [{"channel_name": "RadianteLayer", "channel_type": "WorldStatic"}, {"channel_name": "ZephyrLayer", "channel_type": "WorldStatic"}, {"channel_name": "UmbralLayer", "channel_type": "WorldStatic"}, {"channel_name": "VerticalConnector", "channel_type": "WorldDynamic"}], "chaos_settings": {"enable_chaos": true, "solver_iterations": 8, "collision_margin": 0.1, "sleep_threshold": 0.05}}}
[2025.08.28-02.04.25:366][413]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_collision_profiles
[2025.08.28-02.04.25:367][413]LogTemp: FUnrealMCPCollisionCommands::HandleCommand - Processing: create_layer_collision_profiles
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer0_Profile for layer 0 (PlanicieRadiante) with collision enabled: 3
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created profile Layer0_Profile for layer 0 (PlanicieRadiante)
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer1_Profile for layer 1 (FirmamentoZephyr) with collision enabled: 3
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created profile Layer1_Profile for layer 1 (FirmamentoZephyr)
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field profile_name was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.28-02.04.25:368][413]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer2_Profile for layer 2 (AbismoUmbral) with collision enabled: 3
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created profile Layer2_Profile for layer 2 (AbismoUmbral)
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created custom channel RadianteLayer (WorldStatic) mapped to channel 14
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created custom channel ZephyrLayer (WorldStatic) mapped to channel 14
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created custom channel UmbralLayer (WorldStatic) mapped to channel 14
[2025.08.28-02.04.25:368][413]LogTemp: Layer Collision Profiles: Created custom channel VerticalConnector (WorldDynamic) mapped to channel 14
[2025.08.28-02.04.25:368][413]LogJson: Warning: Field enable_ccd was not found.
[2025.08.28-02.04.25:368][413]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.04.25:369][413]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 0 (Iterations: 10.000000, Margin: 0.050000, CCD: No)
[2025.08.28-02.04.25:369][413]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 0
[2025.08.28-02.04.25:369][413]LogJson: Warning: Field enable_ccd was not found.
[2025.08.28-02.04.25:369][413]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.04.25:369][413]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 1 (Iterations: 6.000000, Margin: 0.200000, CCD: No)
[2025.08.28-02.04.25:369][413]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 1
[2025.08.28-02.04.25:369][413]LogJson: Warning: Field enable_ccd was not found.
[2025.08.28-02.04.25:369][413]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.04.25:369][413]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 2 (Iterations: 12.000000, Margin: 0.030000, CCD: Yes)
[2025.08.28-02.04.25:369][413]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 2
[2025.08.28-02.04.25:369][413]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Collision/Profiles/AuracronCollisionSystem.AuracronCollisionSystem' could not be found in the Asset Registry.
[2025.08.28-02.04.25:373][413]LogTemp: Layer Collision Profiles system created: AuracronCollisionSystem (Profiles: 3, Layers: 3, Saved: No)
[2025.08.28-02.04.25:373][413]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"profile_system_name": "AuracronCollisionSystem",
		"package_path": "/Game/Auracron/Collision/Profiles/AuracronCollisionSystem",
		"profiles_created": 3,
		"layers_configured": 3,
		"saved_to_disk": false,
		"config_path": "../../../../../../Game/AURACRON/Content/Auracron/Collision/Profiles/AuracronCollisionSystem_Config.json",
		"configuration":
		{
			"profile_system_name": "AuracronCollisionSystem",
			"layer_profiles": [
				{
					"layer_name": "PlanicieRadiante",
					"collision_enabled": "collision_enabled",
					"object_type": "RadianteLayer",
					"collision_responses":
					{
						"RadianteLayer": "block",
						"ZephyrLayer": "ignore",
						"UmbralLayer": "ignore",
						"VerticalConnector": "overlap"
					}
				},
				{
					"layer_name": "FirmamentoZephyr",
					"collision_enabled": "collision_enabled",
					"object_type": "ZephyrLayer",
					"collision_responses":
					{
						"RadianteLayer": "ignore",
						"ZephyrLayer": "block",
						"UmbralLayer": "ignore",
						"VerticalConnector": "overlap"
					}
				},
				{
					"layer_name": "AbismoUmbral",
					"collision_enabled": "collision_enabled",
					"object_type": "UmbralLayer",
					"collision_responses":
					{
						"RadianteLayer": "ignore",
						"ZephyrLayer": "ignore",
						"UmbralLayer": "block",
						"VerticalConnector": "overlap"
					}
				}
			],
			"profiles_created": 3
		},
		"created_profiles": [
			{
				"layer_index": 0,
				"profile_name": "Layer0_Profile"
			},
			{
				"layer_index": 1,
				"profile_name": "Layer1_Profile"
			},
			{
				"layer_index": 2,
				"profile_name": "Layer2_Profile"
			}
		]
	}
}
[2025.08.28-02.04.25:374][413]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1732
[2025.08.28-02.04.25:374][413]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.33:220][539]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.33:220][539]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.33:221][539]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.33:322][541]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.33:322][541]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.33:322][541]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_pathfinding", "params": {"system_name": "AuracronPathfindingSystem", "layer_count": 3, "layer_heights": [1000.0, 3000.0, 5000.0], "transition_costs": {"portal_primary": 2.0, "elevator_mystic": 3.0, "bridge_dimensional": 1.5, "emergency_exit": 4.0}, "heuristic_settings": {"base_weight": 1, "vertical_penalty": 1.5, "layer_preference": 1.2, "distance_multiplier": 1}}}
[2025.08.28-02.04.33:322][541]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_pathfinding
[2025.08.28-02.04.33:323][541]LogTemp: Multilayer Pathfinding: Set transition cost portal_primary = 2.000000
[2025.08.28-02.04.33:323][541]LogTemp: Multilayer Pathfinding: Set transition cost elevator_mystic = 3.000000
[2025.08.28-02.04.33:323][541]LogTemp: Multilayer Pathfinding: Set transition cost bridge_dimensional = 1.500000
[2025.08.28-02.04.33:324][541]LogTemp: Multilayer Pathfinding: Set transition cost emergency_exit = 4.000000
[2025.08.28-02.04.33:324][541]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Pathfinding/AuracronPathfindingSystem.AuracronPathfindingSystem' could not be found in the Asset Registry.
[2025.08.28-02.04.33:324][541]LogTemp: Multilayer Pathfinding system created and saved: /Game/Auracron/Pathfinding/AuracronPathfindingSystem (Layers: 3, Saved: No)
[2025.08.28-02.04.33:324][541]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"system_name": "AuracronPathfindingSystem",
		"package_path": "/Game/Auracron/Pathfinding/AuracronPathfindingSystem",
		"layer_count": 3,
		"saved_to_disk": false,
		"full_path": "../../../../../../Game/AURACRON/Content/Auracron/Pathfinding/AuracronPathfindingSystem.uasset",
		"layer_configuration": [
			{
				"layer_index": 0,
				"height": 1000,
				"navigation_data_available": false
			},
			{
				"layer_index": 1,
				"height": 3000,
				"navigation_data_available": false
			},
			{
				"layer_index": 2,
				"height": 5000,
				"navigation_data_available": false
			}
		],
		"transition_costs":
		{
			"portal_primary": 2,
			"elevator_mystic": 3,
			"bridge_dimensional": 1.5,
			"emergency_exit": 4
		}
	}
}
[2025.08.28-02.04.33:324][541]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 793
[2025.08.28-02.04.33:324][541]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.42:679][737]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.42:679][737]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.42:679][737]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.42:780][737]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.42:780][737]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.42:780][737]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_fog_of_war", "params": {"system_name": "AuracronFogOfWarSystem", "update_frequency": 0.1, "performance_mode": "high", "layer_configs": [{"layer_name": "PlanicieRadiante", "fog_density": 0.3, "visibility_range": 1200, "vertical_range": 400, "fog_color": [0.8, 0.9, 0.7, 0.5]}, {"layer_name": "FirmamentoZephyr", "fog_density": 0.5, "visibility_range": 1000, "vertical_range": 600, "fog_color": [0.7, 0.8, 1, 0.6]}, {"layer_name": "AbismoUmbral", "fog_density": 0.8, "visibility_range": 800, "vertical_range": 300, "fog_color": [0.3, 0.2, 0.5, 0.8]}], "visibility_ranges": {"horizontal_same_layer": 1200.0, "vertical_adjacent": 400.0, "ward_horizontal": 1600.0, "ward_vertical": 600.0}}}
[2025.08.28-02.04.42:780][737]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_fog_of_war
[2025.08.28-02.04.42:787][737]LogTemp: Created fog parameter collection: AuracronFogOfWarSystem_FogParams with 3 layers
[2025.08.28-02.04.42:787][737]LogTemp: Multilayer Fog of War: Created fog actor for layer 0 at height 1000.000000
[2025.08.28-02.04.42:788][737]LogTemp: Multilayer Fog of War: Created fog actor for layer 1 at height 3000.000000
[2025.08.28-02.04.42:788][737]LogTemp: Multilayer Fog of War: Created fog actor for layer 2 at height 5000.000000
[2025.08.28-02.04.42:788][737]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem.AuracronFogOfWarSystem' could not be found in the Asset Registry.
[2025.08.28-02.04.42:788][737]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.04.42:845][737]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams] ([1] browsable assets)...
[2025.08.28-02.04.42:845][737]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams]
[2025.08.28-02.04.42:853][737]LogSavePackage: Moving output files for package: /Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams
[2025.08.28-02.04.42:853][737]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AuracronFogOfWarSystem_FogParamsAC489D63413809966ECCDBA6CFBEEA70.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams.uasset'
[2025.08.28-02.04.42:871][737]LogFileHelpers: InternalPromptForCheckoutAndSave took 81.883 ms (total: 2.84 sec)
[2025.08.28-02.04.42:871][737]LogTemp: Multilayer Fog of War system created: AuracronFogOfWarSystem (Layers: 3, Performance: high, Saved: No)
[2025.08.28-02.04.42:871][737]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"system_name": "AuracronFogOfWarSystem",
		"package_path": "/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem",
		"parameter_collection_path": "/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams",
		"layer_count": 3,
		"performance_mode": "high",
		"update_frequency": 0.05000000074505806,
		"saved_to_disk": false,
		"full_path": "../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem.uasset",
		"fog_actors": [
			{
				"layer_index": 0,
				"actor_name": "FogOfWar_Layer_0_AuracronFogOfWarSystem",
				"height": 1000
			},
			{
				"layer_index": 1,
				"actor_name": "FogOfWar_Layer_1_AuracronFogOfWarSystem",
				"height": 3000
			},
			{
				"layer_index": 2,
				"actor_name": "FogOfWar_Layer_2_AuracronFogOfWarSystem",
				"height": 5000
			}
		]
	}
}
[2025.08.28-02.04.42:871][737]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 880
[2025.08.28-02.04.42:871][737]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.42:883][738]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.04.43:160][738]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.04.43:161][738]LogContentValidation: Enabled validators:
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.04.43:161][738]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.04.43:161][738]AssetCheck: /Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams Validando ativo
[2025.08.28-02.04.51:930][770]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.51:930][770]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.51:930][770]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.52:030][770]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.52:030][770]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.52:030][770]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_3d_heatmap_system", "params": {"heatmap_system_name": "AuracronAnalyticsSystem", "tracking_categories": ["movement", "combat", "deaths", "objectives", "layer_transitions", "portal_usage", "elevator_usage"], "layer_configurations": [{"layer_name": "PlanicieRadiante", "resolution": 64, "tracking_intensity": "high", "data_retention": 7200}, {"layer_name": "FirmamentoZephyr", "resolution": 48, "tracking_intensity": "medium", "data_retention": 5400}, {"layer_name": "AbismoUmbral", "resolution": 32, "tracking_intensity": "high", "data_retention": 3600}], "resolution_settings": {"spatial_resolution": 100.0, "temporal_sampling": 0.5, "data_compression": 0.8}, "visualization_settings": {"enable_realtime": true, "color_scheme": "thermal", "opacity": 0.7, "update_interval": 1}}}
[2025.08.28-02.04.52:030][770]LogTemp: Display: UnrealMCPBridge: Executing command: create_3d_heatmap_system
[2025.08.28-02.04.52:031][770]LogAnalytics: Warning: CreateAnalyticsProvider config not contain required parameter APIKeyET
[2025.08.28-02.04.52:031][770]LogTemp: Error: InitializeAnalyticsProvider: Failed to create modern UE 5.6.1 analytics provider AuracronAnalyticsSystem
[2025.08.28-02.04.52:031][770]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Failed to initialize heatmap analytics provider"
}
[2025.08.28-02.04.52:031][770]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 86
[2025.08.28-02.04.52:031][770]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.04.59:467][913]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.59:467][913]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.59:467][913]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.04.59:568][915]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.04.59:569][915]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.04.59:569][915]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_advanced_balance_metrics", "params": {"balance_system_name": "AuracronBalanceSystem", "metric_categories": ["tower_control", "objective_control", "layer_utilization", "transition_frequency", "resource_distribution"], "imbalance_thresholds": {"tower_control_deviation": 0.15, "objective_time_deviation": 0.2, "layer_usage_deviation": 0.25, "transition_rate_deviation": 0.3}, "automated_alerts": {"enable_alerts": true, "alert_threshold": 0.2, "notification_interval": 300}, "correction_suggestions": {"enable_suggestions": true, "suggestion_confidence": 0.8, "auto_apply": false}}}
[2025.08.28-02.04.59:569][915]LogTemp: Display: UnrealMCPBridge: Executing command: create_advanced_balance_metrics
[2025.08.28-02.04.59:570][915]LogTemp: Advanced Balance Metrics: Initialized tracking for tower_control
[2025.08.28-02.04.59:570][915]LogTemp: Advanced Balance Metrics: Initialized tracking for objective_control
[2025.08.28-02.04.59:570][915]LogTemp: Advanced Balance Metrics: Initialized tracking for layer_utilization
[2025.08.28-02.04.59:570][915]LogTemp: Advanced Balance Metrics: Initialized tracking for transition_frequency
[2025.08.28-02.04.59:570][915]LogTemp: Advanced Balance Metrics: Initialized tracking for resource_distribution
[2025.08.28-02.04.59:570][915]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem.AuracronBalanceSystem' could not be found in the Asset Registry.
[2025.08.28-02.04.59:577][915]LogTemp: Advanced Balance Metrics system created: AuracronBalanceSystem (Metrics: 5, Categories: 5, Saved: No)
[2025.08.28-02.04.59:578][915]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"balance_system_name": "AuracronBalanceSystem",
		"package_path": "/Game/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem",
		"metrics_initialized": 5,
		"metric_categories_count": 5,
		"saved_to_disk": false,
		"config_path": "../../../../../../Game/AURACRON/Content/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem_Config.json",
		"configuration":
		{
			"balance_system_name": "AuracronBalanceSystem",
			"metric_categories": [],
			"imbalance_thresholds":
			{
				"tower_control_deviation": 0.14999999999999999,
				"objective_time_deviation": 0.20000000000000001,
				"layer_usage_deviation": 0.25,
				"transition_rate_deviation": 0.29999999999999999
			},
			"automated_alerts":
			{
				"enable_alerts": true,
				"alert_threshold": 0.20000000000000001,
				"notification_interval": 300
			},
			"metrics_initialized": 5
		}
	}
}
[2025.08.28-02.04.59:578][915]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 922
[2025.08.28-02.04.59:578][915]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.05:008][ 32]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.05:008][ 32]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.05:008][ 32]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.05:109][ 34]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.05:109][ 34]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.05:109][ 34]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.28-02.05.05:109][ 34]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.28-02.05.05:110][ 34]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
[2025.08.28-02.05.05:110][ 34]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlanicieRadiante_Base",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FirmamentoZephyr_Base",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 3000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbismoUmbral_Base",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 5000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlanicieRadiante_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "PlanicieRadiante_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "PlanicieRadiante_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "FirmamentoZephyr_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "FirmamentoZephyr_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "FirmamentoZephyr_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "AbismoUmbral_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "AbismoUmbral_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "AbismoUmbral_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "TopLane_PlanicieRadiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MidLane_PlanicieRadiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "BotLane_PlanicieRadiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Top_T1",
				"class": "BP_Torre_Azul_Top_T1_C",
				"location": [ -5000, 5000, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Top_T2",
				"class": "BP_Torre_Azul_Top_T2_C",
				"location": [ -3500, 3500, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Mid_T1",
				"class": "BP_Torre_Azul_Mid_T1_C",
				"location": [ -4000, 0, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Bot_T1",
				"class": "BP_Torre_Azul_Bot_T1_C",
				"location": [ -5000, -5000, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Top_T1",
				"class": "BP_Torre_Vermelha_Top_T1_C",
				"location": [ 5000, -5000, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Mid_T1",
				"class": "BP_Torre_Vermelha_Mid_T1_C",
				"location": [ 4000, 0, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Bot_T1",
				"class": "BP_Torre_Vermelha_Bot_T1_C",
				"location": [ 5000, 5000, 200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Nexus_Azul_PlanicieRadiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Nexus_Vermelho_PlanicieRadiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Azul_Pequeno_1",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Azul_Medio_1",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Buff_Luz_Azul",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Buff_Crescimento_Azul",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Buff_Luz_Vermelho",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Buff_Crescimento_Vermelho",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Guardiao_da_Aurora",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Top_River",
				"class": "StaticMeshActor",
				"location": [ 0, 6000, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Mid_Center",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Bot_River",
				"class": "StaticMeshActor",
				"location": [ 0, -6000, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Jungle_NE",
				"class": "StaticMeshActor",
				"location": [ 4500, 4500, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Jungle_NW",
				"class": "StaticMeshActor",
				"location": [ -4500, 4500, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Portal_Jungle_SE",
				"class": "StaticMeshActor",
				"location": [ 4500, -4500, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Elevador_Norte",
				"class": "StaticMeshActor",
				"location": [ 0, 7500, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Elevador_Sul",
				"class": "StaticMeshActor",
				"location": [ 0, -7500, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Elevador_Leste",
				"class": "StaticMeshActor",
				"location": [ 7500, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Elevador_Oeste",
				"class": "StaticMeshActor",
				"location": [ -7500, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Corrente_Vento_Norte",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Corrente_Vento_Sul",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Zephyr_Norte_1",
				"class": "BP_Torre_Azul_Zephyr_Norte_1_C",
				"location": [ -4000, 4000, 2200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Zephyr_Sul_1",
				"class": "BP_Torre_Azul_Zephyr_Sul_1_C",
				"location": [ -4000, -4000, 2200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Zephyr_Norte_1",
				"class": "BP_Torre_Vermelha_Zephyr_Norte_1_C",
				"location": [ 4000, 4000, 2200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Zephyr_Sul_1",
				"class": "BP_Torre_Vermelha_Zephyr_Sul_1_C",
				"location": [ 4000, -4000, 2200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Senhor_dos_Ventos",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Camp_Tempestade_Azul_1",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "JungleStructure_monster_den_L2",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Azul_Umbral_1",
				"class": "BP_Torre_Azul_Umbral_1_C",
				"location": [ -3000, 3000, 4300 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Torre_Vermelha_Umbral_1",
				"class": "BP_Torre_Vermelha_Umbral_1_C",
				"location": [ 3000, -3000, 4300 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Arqui_Sombra",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Luz_PlanicieRadiante_Principal",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Luz_FirmamentoZephyr_Principal",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Luz_AbismoUmbral_Principal",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Efeito_PlanicieRadiante_Cristais",
				"class": "Actor",
				"location": [ 0, 0, 1200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Efeito_FirmamentoZephyr_Ventos",
				"class": "Actor",
				"location": [ 0, 0, 3200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Efeito_AbismoUmbral_Sombras",
				"class": "Actor",
				"location": [ 0, 0, 5200 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FogOfWar_Layer_0_AuracronFogOfWarSystem",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FogOfWar_Layer_1_AuracronFogOfWarSystem",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 3000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FogOfWar_Layer_2_AuracronFogOfWarSystem",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 5000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		]
	}
}
[2025.08.28-02.05.05:111][ 34]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 11984
[2025.08.28-02.05.05:111][ 34]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.22:641][ 99]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.22:641][ 99]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.22:641][ 99]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.22:743][ 99]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.22:743][ 99]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.22:744][ 99]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T3", "tower_type": "nexus", "location": {"x": -2000.0, "y": 2000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "crystal_fortress", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage", "regeneration"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams", "healing_particles"]}}}
[2025.08.28-02.05.22:744][ 99]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.22:793][ 99]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.22:793][ 99]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.22:793][ 99]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.22:815][ 99]LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
[2025.08.28-02.05.22:820][ 99]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T3 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
[2025.08.28-02.05.22:821][ 99]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T3 spawned in world at location (-2000.0, 2000.0, 200.0)
[2025.08.28-02.05.22:821][ 99]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.22:885][ 99]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3] ([2] browsable assets)...
[2025.08.28-02.05.22:885][ 99]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.08.28-02.05.22:930][ 99]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.BP_Torre_Azul_Top_T3]
[2025.08.28-02.05.22:930][ 99]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3]
[2025.08.28-02.05.22:930][ 99]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.uasset" SILENT=true
[2025.08.28-02.05.22:940][ 99]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
[2025.08.28-02.05.22:940][ 99]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T3421FD0A946C93E2685AE4BA9DB667869.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.uasset'
[2025.08.28-02.05.22:956][ 99]LogFileHelpers: InternalPromptForCheckoutAndSave took 136.124 ms (total: 2.98 sec)
[2025.08.28-02.05.22:956][ 99]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T3 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
[2025.08.28-02.05.22:956][ 99]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T3 (Type: nexus, Layer: 0, Team: 0, Height: 1200.0)
[2025.08.28-02.05.22:956][ 99]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Top_T3",
		"tower_type": "nexus",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 1200,
		"tower_radius": 200,
		"tower_levels": 7,
		"hierarchical_instancing": true,
		"pcg_generation": true,
		"success": true,
		"timestamp": "2025.08.27-23.05.22",
		"location":
		{
			"x": -2000,
			"y": 2000,
			"z": 200
		}
	}
}
[2025.08.28-02.05.22:956][ 99]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
[2025.08.28-02.05.22:956][ 99]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.22:968][100]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.23:181][100]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.23:181][100]LogContentValidation: Enabled validators:
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.23:181][100]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.23:181][100]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3 Validando ativo
[2025.08.28-02.05.28:700][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.28:700][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.28:700][117]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.28:800][118]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.28:800][118]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.28:800][118]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Mid_T2", "tower_type": "advanced", "location": {"x": -2500.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
[2025.08.28-02.05.28:800][118]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.29:128][118]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.29:128][118]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.29:128][118]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.29:146][118]LogUObjectHash: Compacting FUObjectHashTables data took   0.49ms
[2025.08.28-02.05.29:149][118]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Mid_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
[2025.08.28-02.05.29:151][118]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Mid_T2 spawned in world at location (-2500.0, 0.0, 200.0)
[2025.08.28-02.05.29:151][118]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.29:214][118]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2] ([2] browsable assets)...
[2025.08.28-02.05.29:214][118]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.08.28-02.05.29:250][118]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.BP_Torre_Azul_Mid_T2]
[2025.08.28-02.05.29:250][118]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2]
[2025.08.28-02.05.29:250][118]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.uasset" SILENT=true
[2025.08.28-02.05.29:259][118]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
[2025.08.28-02.05.29:259][118]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Mid_T2DD332E2448A94C250008159C24027FE6.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.uasset'
[2025.08.28-02.05.29:275][118]LogFileHelpers: InternalPromptForCheckoutAndSave took 124.776 ms (total: 3.10 sec)
[2025.08.28-02.05.29:275][118]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Mid_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
[2025.08.28-02.05.29:275][118]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Mid_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-02.05.29:275][118]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Mid_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.05.29",
		"location":
		{
			"x": -2500,
			"y": 0,
			"z": 200
		}
	}
}
[2025.08.28-02.05.29:275][118]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
[2025.08.28-02.05.29:275][118]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.29:283][119]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.29:514][119]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.29:514][119]LogContentValidation: Enabled validators:
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.29:514][119]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.29:514][119]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2 Validando ativo
[2025.08.28-02.05.35:547][138]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.35:547][138]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.35:547][138]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.35:648][138]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.35:648][138]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.35:648][138]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Bot_T2", "tower_type": "advanced", "location": {"x": -3500.0, "y": -3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
[2025.08.28-02.05.35:648][138]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.35:796][138]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.35:796][138]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.35:796][138]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.35:824][138]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.08.28-02.05.35:826][138]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Bot_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
[2025.08.28-02.05.35:827][138]LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Bot_T2 spawned in world at location (-3500.0, -3500.0, 200.0)
[2025.08.28-02.05.35:827][138]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.35:879][138]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2] ([2] browsable assets)...
[2025.08.28-02.05.35:879][138]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.08.28-02.05.35:915][138]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.BP_Torre_Azul_Bot_T2]
[2025.08.28-02.05.35:915][138]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2]
[2025.08.28-02.05.35:915][138]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.uasset" SILENT=true
[2025.08.28-02.05.35:926][138]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
[2025.08.28-02.05.35:926][138]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Bot_T2C7C2528946D80F1CE1B39B81CC4816E7.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.uasset'
[2025.08.28-02.05.35:944][138]LogFileHelpers: InternalPromptForCheckoutAndSave took 116.305 ms (total: 3.22 sec)
[2025.08.28-02.05.35:944][138]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Bot_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
[2025.08.28-02.05.35:944][138]LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Bot_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-02.05.35:944][138]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Azul_Bot_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.05.35",
		"location":
		{
			"x": -3500,
			"y": -3500,
			"z": 200
		}
	}
}
[2025.08.28-02.05.35:944][138]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 459
[2025.08.28-02.05.35:944][138]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.35:955][139]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.36:177][139]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.36:177][139]LogContentValidation: Enabled validators:
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.36:177][139]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.36:177][139]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2 Validando ativo
[2025.08.28-02.05.41:895][163]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.41:895][163]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.41:895][163]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.41:995][164]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.41:995][164]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.41:995][164]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Top_T2", "tower_type": "advanced", "location": {"x": 3500.0, "y": -3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
[2025.08.28-02.05.41:995][164]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.41:995][164]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.41:995][164]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.41:995][164]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.42:023][164]LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
[2025.08.28-02.05.42:026][164]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Top_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
[2025.08.28-02.05.42:026][164]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Top_T2 spawned in world at location (3500.0, -3500.0, 200.0)
[2025.08.28-02.05.42:026][164]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.42:028][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:084][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:135][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:144][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:146][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:147][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:148][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:148][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:149][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:150][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:151][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:152][164]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2] ([2] browsable assets)...
[2025.08.28-02.05.42:152][164]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.08.28-02.05.42:153][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:155][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:172][164]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.BP_Torre_Vermelha_Top_T2]
[2025.08.28-02.05.42:172][164]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2]
[2025.08.28-02.05.42:172][164]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.uasset" SILENT=true
[2025.08.28-02.05.42:182][164]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
[2025.08.28-02.05.42:182][164]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Top_T23319CBEB48C9088D870642AD287BBC4F.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.uasset'
[2025.08.28-02.05.42:185][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:187][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:187][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:188][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:189][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:189][164]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.42:200][164]LogFileHelpers: InternalPromptForCheckoutAndSave took 172.679 ms (total: 3.39 sec)
[2025.08.28-02.05.42:200][164]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Top_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
[2025.08.28-02.05.42:200][164]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Top_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-02.05.42:200][164]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Top_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.05.42",
		"location":
		{
			"x": 3500,
			"y": -3500,
			"z": 200
		}
	}
}
[2025.08.28-02.05.42:200][164]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 462
[2025.08.28-02.05.42:200][164]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.42:201][164]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.42:261][165]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.42:261][165]LogContentValidation: Enabled validators:
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.42:261][165]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.42:261][165]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2 Validando ativo
[2025.08.28-02.05.48:435][292]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.48:435][292]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.48:435][292]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.05.48:536][294]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.48:536][294]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.48:536][294]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Mid_T2", "tower_type": "advanced", "location": {"x": 2500.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
[2025.08.28-02.05.48:536][294]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.48:537][294]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.48:537][294]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.48:537][294]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.48:561][294]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.28-02.05.48:566][294]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Mid_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
[2025.08.28-02.05.48:568][294]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Mid_T2 spawned in world at location (2500.0, 0.0, 200.0)
[2025.08.28-02.05.48:568][294]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.48:569][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:631][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:680][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:682][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:683][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:683][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:684][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:685][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:685][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:686][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:686][294]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2] ([2] browsable assets)...
[2025.08.28-02.05.48:686][294]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.08.28-02.05.48:688][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:690][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:707][294]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.BP_Torre_Vermelha_Mid_T2]
[2025.08.28-02.05.48:707][294]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2]
[2025.08.28-02.05.48:707][294]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.uasset" SILENT=true
[2025.08.28-02.05.48:719][294]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
[2025.08.28-02.05.48:719][294]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Mid_T273236AFA47B82B33C7842FB8E5F39BDD.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.uasset'
[2025.08.28-02.05.48:724][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:724][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:725][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:726][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:726][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:727][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:738][294]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.48:748][294]LogFileHelpers: InternalPromptForCheckoutAndSave took 179.879 ms (total: 3.57 sec)
[2025.08.28-02.05.48:748][294]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Mid_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
[2025.08.28-02.05.48:748][294]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Mid_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-02.05.48:748][294]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Mid_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.05.48",
		"location":
		{
			"x": 2500,
			"y": 0,
			"z": 200
		}
	}
}
[2025.08.28-02.05.48:748][294]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
[2025.08.28-02.05.48:748][294]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.48:748][294]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.48:809][295]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.48:809][295]LogContentValidation: Enabled validators:
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.48:809][295]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.48:809][295]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2 Validando ativo
[2025.08.28-02.05.54:985][409]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.54:985][409]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.54:985][409]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.55:087][410]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.05.55:087][410]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.05.55:087][410]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Bot_T2", "tower_type": "advanced", "location": {"x": 3500.0, "y": 3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
[2025.08.28-02.05.55:087][410]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.05.55:088][410]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.05.55:088][410]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.05.55:088][410]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.05.55:136][410]LogUObjectHash: Compacting FUObjectHashTables data took   1.16ms
[2025.08.28-02.05.55:141][410]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Bot_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
[2025.08.28-02.05.55:143][410]LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Bot_T2 spawned in world at location (3500.0, 3500.0, 200.0)
[2025.08.28-02.05.55:143][410]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.05.55:144][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:212][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:252][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:253][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:254][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:256][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:256][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:257][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:258][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:259][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:259][410]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2] ([2] browsable assets)...
[2025.08.28-02.05.55:260][410]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.08.28-02.05.55:263][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:265][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:288][410]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.BP_Torre_Vermelha_Bot_T2]
[2025.08.28-02.05.55:288][410]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2]
[2025.08.28-02.05.55:288][410]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.uasset" SILENT=true
[2025.08.28-02.05.55:305][410]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
[2025.08.28-02.05.55:305][410]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Bot_T214D25C9346325ABC56E53B81B0B1461B.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.uasset'
[2025.08.28-02.05.55:312][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:313][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:313][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:315][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:315][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:316][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:325][410]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.05.55:334][410]LogFileHelpers: InternalPromptForCheckoutAndSave took 191.671 ms (total: 3.77 sec)
[2025.08.28-02.05.55:335][410]LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Bot_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
[2025.08.28-02.05.55:335][410]LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Bot_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
[2025.08.28-02.05.55:335][410]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "Torre_Vermelha_Bot_T2",
		"tower_type": "advanced",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 800,
		"tower_radius": 150,
		"tower_levels": 5,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.05.55",
		"location":
		{
			"x": 3500,
			"y": 3500,
			"z": 200
		}
	}
}
[2025.08.28-02.05.55:335][410]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 461
[2025.08.28-02.05.55:335][410]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.05.55:346][410]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.05.55:415][411]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.05.55:416][411]LogContentValidation: Enabled validators:
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.05.55:416][411]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.05.55:416][411]AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2 Validando ativo
[2025.08.28-02.06.03:590][550]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.03:590][550]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.03:590][550]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.03:691][550]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.03:691][550]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.03:691][550]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Pequeno_2", "camp_type": "small", "location": {"x": -2000.0, "y": -2500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "light_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "light_blessing"}, "respawn_timer": 60}}}
[2025.08.28-02.06.03:692][550]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.03:761][550]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.03:761][550]LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Pequeno_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.03:761][550]LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Pequeno_2 (Type: small, Layer: 0)
[2025.08.28-02.06.03:761][550]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Azul_Pequeno_2",
		"camp_type": "small",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.06.03"
	}
}
[2025.08.28-02.06.03:761][550]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 227
[2025.08.28-02.06.03:761][550]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.11:014][588]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.11:014][588]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.11:014][588]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.11:114][588]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.11:114][588]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.11:114][588]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Medio_2", "camp_type": "medium", "location": {"x": -1800.0, "y": 3200.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "crystal_golem", "count": 1, "level": 3}, {"type": "light_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "crystal_armor"}, "respawn_timer": 90}}}
[2025.08.28-02.06.11:114][588]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.11:187][588]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.11:187][588]LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Medio_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.11:187][588]LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Medio_2 (Type: medium, Layer: 0)
[2025.08.28-02.06.11:187][588]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Azul_Medio_2",
		"camp_type": "medium",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.06.11"
	}
}
[2025.08.28-02.06.11:187][588]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
[2025.08.28-02.06.11:187][588]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.16:930][606]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.16:930][606]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.16:930][606]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.17:031][606]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.17:031][606]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.17:031][606]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vermelho_Pequeno_1", "camp_type": "small", "location": {"x": 3000.0, "y": -2000.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "shadow_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "shadow_blessing"}, "respawn_timer": 60}}}
[2025.08.28-02.06.17:031][606]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.17:187][606]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.17:188][606]LogTemp: CreateRobustTowerStructure: Created tower Camp_Vermelho_Pequeno_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.17:188][606]LogTemp: HandleCreateJungleCamps: Created camp Camp_Vermelho_Pequeno_1 (Type: small, Layer: 0)
[2025.08.28-02.06.17:188][606]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Vermelho_Pequeno_1",
		"camp_type": "small",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.06.17"
	}
}
[2025.08.28-02.06.17:188][606]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
[2025.08.28-02.06.17:188][606]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.23:227][625]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.23:227][625]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.23:227][625]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.23:328][625]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.23:328][625]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.23:328][625]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vermelho_Medio_1", "camp_type": "medium", "location": {"x": 2500.0, "y": -3500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "shadow_golem", "count": 1, "level": 3}, {"type": "shadow_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "shadow_armor"}, "respawn_timer": 90}}}
[2025.08.28-02.06.23:328][625]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.23:524][625]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.23:524][625]LogTemp: CreateRobustTowerStructure: Created tower Camp_Vermelho_Medio_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.23:524][625]LogTemp: HandleCreateJungleCamps: Created camp Camp_Vermelho_Medio_1 (Type: medium, Layer: 0)
[2025.08.28-02.06.23:524][625]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Vermelho_Medio_1",
		"camp_type": "medium",
		"layer_index": 0,
		"success": true,
		"timestamp": "2025.08.27-23.06.23"
	}
}
[2025.08.28-02.06.23:524][625]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
[2025.08.28-02.06.23:524][625]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.31:594][650]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.31:594][650]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.31:594][650]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.31:697][650]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.31:697][650]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.31:697][650]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Azul_2", "camp_type": "medium", "location": {"x": -2500.0, "y": -2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "storm_elemental", "count": 1, "level": 4}, {"type": "wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "storm_speed"}, "respawn_timer": 90}}}
[2025.08.28-02.06.31:697][650]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.31:859][650]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.31:859][650]LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Azul_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.31:859][650]LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Azul_2 (Type: medium, Layer: 1)
[2025.08.28-02.06.31:859][650]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Tempestade_Azul_2",
		"camp_type": "medium",
		"layer_index": 1,
		"success": true,
		"timestamp": "2025.08.27-23.06.31"
	}
}
[2025.08.28-02.06.31:859][650]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
[2025.08.28-02.06.31:859][650]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.38:828][671]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.38:828][671]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.38:828][671]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.38:934][672]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.38:934][672]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.38:934][672]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vento_Azul_1", "camp_type": "large", "location": {"x": -1800.0, "y": 1800.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "wind_lord_minor", "count": 1, "level": 6}, {"type": "storm_elemental", "count": 2, "level": 4}], "reward_systems": {"gold": 250, "experience": 350, "buff": "wind_mastery_minor"}, "respawn_timer": 180}}}
[2025.08.28-02.06.38:934][672]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.39:194][672]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.39:194][672]LogTemp: CreateRobustTowerStructure: Created tower Camp_Vento_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.39:194][672]LogTemp: HandleCreateJungleCamps: Created camp Camp_Vento_Azul_1 (Type: large, Layer: 1)
[2025.08.28-02.06.39:194][672]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Vento_Azul_1",
		"camp_type": "large",
		"layer_index": 1,
		"success": true,
		"timestamp": "2025.08.27-23.06.39"
	}
}
[2025.08.28-02.06.39:194][672]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 225
[2025.08.28-02.06.39:194][672]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.45:552][708]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.45:552][708]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.45:553][708]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.45:653][710]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.45:653][710]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.45:653][710]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Vermelho_1", "camp_type": "medium", "location": {"x": 3000.0, "y": 2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "dark_storm_elemental", "count": 1, "level": 4}, {"type": "shadow_wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "dark_storm_speed"}, "respawn_timer": 90}}}
[2025.08.28-02.06.45:653][710]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.45:654][710]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.45:655][710]LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Vermelho_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.45:655][710]LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Vermelho_1 (Type: medium, Layer: 1)
[2025.08.28-02.06.45:655][710]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Tempestade_Vermelho_1",
		"camp_type": "medium",
		"layer_index": 1,
		"success": true,
		"timestamp": "2025.08.27-23.06.45"
	}
}
[2025.08.28-02.06.45:656][710]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 235
[2025.08.28-02.06.45:656][710]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.52:295][840]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.52:295][840]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.52:295][840]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.06.52:396][842]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.52:396][842]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.52:396][842]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Sombra_Azul_1", "camp_type": "large", "location": {"x": -2000.0, "y": 2500.0, "z": 4250.0}, "layer_index": 2, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "umbral_guardian", "count": 1, "level": 8}, {"type": "shadow_wraith", "count": 3, "level": 6}], "reward_systems": {"gold": 400, "experience": 500, "buff": "umbral_stealth"}, "respawn_timer": 120}}}
[2025.08.28-02.06.52:396][842]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.52:397][842]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.52:397][842]LogTemp: CreateRobustTowerStructure: Created tower Camp_Sombra_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.52:397][842]LogTemp: HandleCreateJungleCamps: Created camp Camp_Sombra_Azul_1 (Type: large, Layer: 2)
[2025.08.28-02.06.52:397][842]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Sombra_Azul_1",
		"camp_type": "large",
		"layer_index": 2,
		"success": true,
		"timestamp": "2025.08.27-23.06.52"
	}
}
[2025.08.28-02.06.52:397][842]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
[2025.08.28-02.06.52:397][842]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.59:644][980]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.59:644][980]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.59:644][980]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.06.59:746][982]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.06.59:746][982]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.06.59:746][982]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Sombra_Vermelho_1", "camp_type": "large", "location": {"x": 2000.0, "y": -2500.0, "z": 4250.0}, "layer_index": 2, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "void_guardian", "count": 1, "level": 8}, {"type": "void_wraith", "count": 3, "level": 6}], "reward_systems": {"gold": 400, "experience": 500, "buff": "void_stealth"}, "respawn_timer": 120}}}
[2025.08.28-02.06.59:746][982]LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
[2025.08.28-02.06.59:747][982]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
[2025.08.28-02.06.59:747][982]LogTemp: CreateRobustTowerStructure: Created tower Camp_Sombra_Vermelho_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
[2025.08.28-02.06.59:747][982]LogTemp: HandleCreateJungleCamps: Created camp Camp_Sombra_Vermelho_1 (Type: large, Layer: 2)
[2025.08.28-02.06.59:747][982]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_jungle_camps",
		"camp_name": "Camp_Sombra_Vermelho_1",
		"camp_type": "large",
		"layer_index": 2,
		"success": true,
		"timestamp": "2025.08.27-23.06.59"
	}
}
[2025.08.28-02.06.59:748][982]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
[2025.08.28-02.06.59:748][982]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.07.07:293][150]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.07:293][150]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.07:293][150]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.07.07:395][151]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.07:395][151]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.07:395][151]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_bridge_meshes", "params": {"bridge_name": "Ponte_Altar_Amanhecer", "bridge_type": "dimensional", "start_location": {"x": 2000.0, "y": 3000.0, "z": 1000.0}, "end_location": {"x": 2000.0, "y": 3000.0, "z": 3000.0}, "bridge_settings": {"bridge_width": 300, "support_pillars": false, "decorative_railings": true, "magical_effects": ["dimensional_energy", "crystal_resonance", "light_bridge"]}}}
[2025.08.28-02.07.07:395][151]LogTemp: Display: UnrealMCPBridge: Executing command: create_bridge_meshes
[2025.08.28-02.07.07:396][151]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_bridge_meshes
[2025.08.28-02.07.07:396][151]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameters: bridge_name, start_point, end_point"
}
[2025.08.28-02.07.07:396][151]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 103
[2025.08.28-02.07.07:396][151]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.07.15:042][291]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.15:043][291]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.15:043][291]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.07.15:143][293]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.15:143][293]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.15:143][293]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Altar_Amanhecer", "type": "STATICMESHACTOR", "location": [2000.0, 3000.0, 2000.0], "rotation": [0.0, 0.0, 90.0]}}
[2025.08.28-02.07.15:143][293]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.07.15:145][293]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.07.15:145][293]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Ponte_Altar_Amanhecer",
		"class": "StaticMeshActor",
		"location": [ 2000, 3000, 2000 ],
		"rotation": [ 0, 0, 89.999999999999986 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.07.15:146][293]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 220
[2025.08.28-02.07.15:146][293]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.07.20:679][357]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.20:679][357]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.20:679][357]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.07.20:780][357]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.20:780][357]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.20:780][357]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Templo_Ventos", "type": "STATICMESHACTOR", "location": [3000.0, 2000.0, 3500.0], "rotation": [0.0, 0.0, 45.0]}}
[2025.08.28-02.07.20:780][357]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.07.20:959][357]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.07.20:960][357]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Ponte_Templo_Ventos",
		"class": "StaticMeshActor",
		"location": [ 3000, 2000, 3500 ],
		"rotation": [ 0, 0, 45.000000000000028 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.07.20:960][357]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 218
[2025.08.28-02.07.20:960][357]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.07.25:795][372]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.25:795][372]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.25:795][372]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.07.25:895][372]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.07.25:895][372]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.07.25:895][372]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Cora\u00e7\u00e3o_Trevas", "type": "STATICMESHACTOR", "location": [-2000.0, -3000.0, 2500.0], "rotation": [0.0, 0.0, -45.0]}}
[2025.08.28-02.07.25:895][372]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.28-02.07.25:960][372]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
[2025.08.28-02.07.25:960][372]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "Ponte_Coração_Trevas",
		"class": "StaticMeshActor",
		"location": [ -2000, -3000, 2500 ],
		"rotation": [ 0, 0, -45 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.28-02.07.25:960][372]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 206
[2025.08.28-02.07.25:960][372]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.09.12:043][672]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.09.12:043][672]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.09.12:043][672]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.11.39:068][ 26]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.39:068][ 26]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.39:068][ 26]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.11.39:168][ 26]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.39:168][ 26]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.39:168][ 26]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "PlanicieRadianteLandscape", "layer_index": 0, "size_x": 2017, "size_y": 2017, "scale_x": 100.0, "scale_y": 100.0, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 0.0}, "heightmap_settings": {"noise_type": "perlin", "frequency": 0.01, "amplitude": 500, "octaves": 4, "lacunarity": 2, "persistence": 0.5}}}
[2025.08.28-02.11.39:168][ 26]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.28-02.11.39:324][ 26]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Field use_pcg was not found.
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Field enable_nanite was not found.
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.28-02.11.39:324][ 26]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.28-02.11.39:351][ 26]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadianteLandscape_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-02.11.39:380][ 26]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadianteLandscape_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-02.11.39:409][ 26]LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadianteLandscape_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.28-02.11.39:409][ 26]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for PlanicieRadianteLandscape
[2025.08.28-02.11.39:409][ 26]LogTemp: HandleCreateProceduralLandscape: Created landscape PlanicieRadianteLandscape with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.28-02.11.39:409][ 26]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "PlanicieRadianteLandscape",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-23.11.39"
	}
}
[2025.08.28-02.11.39:409][ 26]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 336
[2025.08.28-02.11.39:409][ 26]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.11.51:002][ 62]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.51:002][ 62]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.51:002][ 62]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.11.51:103][ 62]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.51:103][ 62]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.51:103][ 62]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "TopLane", "lane_type": "top", "start_location": {"x": -8000.0, "y": -8000.0, "z": 100.0}, "end_location": {"x": 8000.0, "y": 8000.0, "z": 100.0}, "lane_width": 1200.0, "geometry_settings": {"segments": 20, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["stone_path", "grass_border"]}}}
[2025.08.28-02.11.51:103][ 62]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-02.11.51:326][ 62]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-02.11.51:326][ 62]LogJson: Warning: Field layer_index was not found.
[2025.08.28-02.11.51:326][ 62]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.11.51:326][ 62]LogTemp: GenerateLaneGeometryWithFlow: Generated lane TopLane with 45 vertices, 64 triangles
[2025.08.28-02.11.51:326][ 62]LogTemp: CreateRobustProceduralMesh: Created mesh TopLane with 56 vertices
[2025.08.28-02.11.51:326][ 62]LogTemp: HandleCreateLaneGeometry: Created lane TopLane for layer 0 (Width: 1200.0, Points: 5)
[2025.08.28-02.11.51:326][ 62]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "TopLane",
		"lane_width": 1200,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-23.11.51"
	}
}
[2025.08.28-02.11.51:326][ 62]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 241
[2025.08.28-02.11.51:326][ 62]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.11.57:574][ 81]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.57:574][ 81]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.57:574][ 81]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.11.57:676][ 82]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.11.57:676][ 82]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.11.57:676][ 82]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "MiddleLane", "lane_type": "middle", "start_location": {"x": -10000.0, "y": 0.0, "z": 100.0}, "end_location": {"x": 10000.0, "y": 0.0, "z": 100.0}, "lane_width": 1400.0, "geometry_settings": {"segments": 25, "elevation_curve": "flat", "side_barriers": true, "material_zones": ["main_road", "tactical_markers"]}}}
[2025.08.28-02.11.57:676][ 82]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-02.11.57:994][ 82]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-02.11.57:994][ 82]LogJson: Warning: Field layer_index was not found.
[2025.08.28-02.11.57:994][ 82]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.11.57:994][ 82]LogTemp: GenerateLaneGeometryWithFlow: Generated lane MiddleLane with 45 vertices, 64 triangles
[2025.08.28-02.11.57:995][ 82]LogTemp: CreateRobustProceduralMesh: Created mesh MiddleLane with 56 vertices
[2025.08.28-02.11.57:995][ 82]LogTemp: HandleCreateLaneGeometry: Created lane MiddleLane for layer 0 (Width: 1400.0, Points: 5)
[2025.08.28-02.11.57:995][ 82]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "MiddleLane",
		"lane_width": 1400,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-23.11.57"
	}
}
[2025.08.28-02.11.57:995][ 82]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 244
[2025.08.28-02.11.57:995][ 82]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.03:737][100]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.03:739][100]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.03:739][100]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.03:839][100]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.03:839][100]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.03:839][100]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "BottomLane", "lane_type": "bottom", "start_location": {"x": -8000.0, "y": 8000.0, "z": 100.0}, "end_location": {"x": 8000.0, "y": -8000.0, "z": 100.0}, "lane_width": 1200.0, "geometry_settings": {"segments": 20, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["stone_path", "grass_border"]}}}
[2025.08.28-02.12.03:839][100]LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
[2025.08.28-02.12.03:994][100]LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
[2025.08.28-02.12.03:994][100]LogJson: Warning: Field layer_index was not found.
[2025.08.28-02.12.03:994][100]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.03:994][100]LogTemp: GenerateLaneGeometryWithFlow: Generated lane BottomLane with 45 vertices, 64 triangles
[2025.08.28-02.12.03:995][100]LogTemp: CreateRobustProceduralMesh: Created mesh BottomLane with 56 vertices
[2025.08.28-02.12.03:995][100]LogTemp: HandleCreateLaneGeometry: Created lane BottomLane for layer 0 (Width: 1200.0, Points: 5)
[2025.08.28-02.12.03:995][100]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_lane_geometry",
		"lane_name": "BottomLane",
		"lane_width": 1200,
		"layer_index": 0,
		"lane_points_count": 5,
		"success": true,
		"timestamp": "2025.08.27-23.12.03"
	}
}
[2025.08.28-02.12.03:995][100]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 244
[2025.08.28-02.12.03:995][100]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.09:839][155]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.09:839][155]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.09:839][155]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.09:940][157]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.09:940][157]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.09:940][157]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "BlueTopTower1", "tower_type": "basic", "location": {"x": -6000.0, "y": -6000.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "radiant_golden", "defensive_features": ["energy_shield", "targeting_system"], "visual_effects": ["golden_glow", "energy_particles"]}}}
[2025.08.28-02.12.09:940][157]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.09:941][157]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.09:941][157]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.09:941][157]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.09:974][157]LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
[2025.08.28-02.12.09:979][157]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_BlueTopTower1 at /Game/Auracron/MOBA/Towers/BP_BlueTopTower1
[2025.08.28-02.12.09:980][157]LogTemp: HandleCreateTowerStructures: Tower BlueTopTower1 spawned in world at location (-6000.0, -6000.0, 100.0)
[2025.08.28-02.12.09:980][157]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.09:981][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:002][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:043][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:045][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:045][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:046][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:047][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:048][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:049][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:050][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:050][157]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_BlueTopTower1] ([2] browsable assets)...
[2025.08.28-02.12.10:051][157]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.08.28-02.12.10:052][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:053][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:070][157]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_BlueTopTower1.BP_BlueTopTower1]
[2025.08.28-02.12.10:070][157]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_BlueTopTower1]
[2025.08.28-02.12.10:070][157]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_BlueTopTower1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueTopTower1.uasset" SILENT=true
[2025.08.28-02.12.10:080][157]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_BlueTopTower1
[2025.08.28-02.12.10:080][157]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_BlueTopTower150B222744218071ECCFCEAA0500DE463.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueTopTower1.uasset'
[2025.08.28-02.12.10:083][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:084][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:084][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:086][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:086][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:089][157]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.10:099][157]LogFileHelpers: InternalPromptForCheckoutAndSave took 118.846 ms (total: 3.88 sec)
[2025.08.28-02.12.10:099][157]LogTemp: HandleCreateTowerStructures: Tower Blueprint BlueTopTower1 successfully saved at /Game/Auracron/MOBA/Towers/BP_BlueTopTower1
[2025.08.28-02.12.10:099][157]LogTemp: HandleCreateTowerStructures: Created tower BlueTopTower1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.10:099][157]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "BlueTopTower1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.10",
		"location":
		{
			"x": -6000,
			"y": -6000,
			"z": 100
		}
	}
}
[2025.08.28-02.12.10:099][157]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 452
[2025.08.28-02.12.10:099][157]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.10:099][157]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.10:166][158]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.10:166][158]LogContentValidation: Enabled validators:
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.10:166][158]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.10:166][158]AssetCheck: /Game/Auracron/MOBA/Towers/BP_BlueTopTower1 Validando ativo
[2025.08.28-02.12.14:867][253]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.08.28-02.12.14:870][253]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.28-02.12.14:897][253]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/AURACRON_Auto1
[2025.08.28-02.12.14:897][253]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Auto1C534B2CB4650388C0DC4BE9B04C6F5CB.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto1.umap'
[2025.08.28-02.12.14:900][253]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/AURACRON' took 0.050
[2025.08.28-02.12.14:900][253]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.050
[2025.08.28-02.12.15:729][272]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.15:729][272]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.15:729][272]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.15:830][274]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.15:830][274]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.15:830][274]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "BlueTopTower2", "tower_type": "basic", "location": {"x": -3000.0, "y": -3000.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "radiant_golden", "defensive_features": ["energy_shield", "targeting_system"], "visual_effects": ["golden_glow", "energy_particles"]}}}
[2025.08.28-02.12.15:830][274]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.15:831][274]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.15:831][274]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.15:831][274]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.15:860][274]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.08.28-02.12.15:864][274]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_BlueTopTower2 at /Game/Auracron/MOBA/Towers/BP_BlueTopTower2
[2025.08.28-02.12.15:865][274]LogTemp: HandleCreateTowerStructures: Tower BlueTopTower2 spawned in world at location (-3000.0, -3000.0, 100.0)
[2025.08.28-02.12.15:865][274]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.15:866][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:933][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:976][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:977][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:978][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:979][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:979][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:980][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:980][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:982][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:982][274]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_BlueTopTower2] ([2] browsable assets)...
[2025.08.28-02.12.15:983][274]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.08.28-02.12.15:984][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.15:986][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:003][274]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_BlueTopTower2.BP_BlueTopTower2]
[2025.08.28-02.12.16:003][274]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_BlueTopTower2]
[2025.08.28-02.12.16:003][274]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_BlueTopTower2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueTopTower2.uasset" SILENT=true
[2025.08.28-02.12.16:012][274]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_BlueTopTower2
[2025.08.28-02.12.16:013][274]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_BlueTopTower2917586FD490099F04081AAA07AE574BD.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueTopTower2.uasset'
[2025.08.28-02.12.16:016][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:016][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:017][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:019][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:019][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:020][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:030][274]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.16:039][274]LogFileHelpers: InternalPromptForCheckoutAndSave took 174.122 ms (total: 4.06 sec)
[2025.08.28-02.12.16:039][274]LogTemp: HandleCreateTowerStructures: Tower Blueprint BlueTopTower2 successfully saved at /Game/Auracron/MOBA/Towers/BP_BlueTopTower2
[2025.08.28-02.12.16:039][274]LogTemp: HandleCreateTowerStructures: Created tower BlueTopTower2 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.16:039][274]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "BlueTopTower2",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.16",
		"location":
		{
			"x": -3000,
			"y": -3000,
			"z": 100
		}
	}
}
[2025.08.28-02.12.16:039][274]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 452
[2025.08.28-02.12.16:039][274]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.16:040][274]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.16:094][275]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.16:094][275]LogContentValidation: Enabled validators:
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.16:094][275]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.16:094][275]AssetCheck: /Game/Auracron/MOBA/Towers/BP_BlueTopTower2 Validando ativo
[2025.08.28-02.12.25:602][502]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.25:603][502]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.25:603][502]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.25:703][504]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.25:703][504]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.25:703][504]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "BlueMidTower1", "tower_type": "basic", "location": {"x": -6000.0, "y": 0.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "radiant_golden", "defensive_features": ["energy_shield", "targeting_system"], "visual_effects": ["golden_glow", "energy_particles"]}}}
[2025.08.28-02.12.25:703][504]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.25:704][504]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.25:704][504]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.25:704][504]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.25:746][504]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.08.28-02.12.25:750][504]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_BlueMidTower1 at /Game/Auracron/MOBA/Towers/BP_BlueMidTower1
[2025.08.28-02.12.25:751][504]LogTemp: HandleCreateTowerStructures: Tower BlueMidTower1 spawned in world at location (-6000.0, 0.0, 100.0)
[2025.08.28-02.12.25:751][504]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.25:752][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:797][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:835][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:855][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:857][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:858][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:860][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:861][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:862][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:862][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:864][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:866][504]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_BlueMidTower1] ([2] browsable assets)...
[2025.08.28-02.12.25:866][504]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.08.28-02.12.25:868][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:869][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:891][504]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_BlueMidTower1.BP_BlueMidTower1]
[2025.08.28-02.12.25:891][504]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_BlueMidTower1]
[2025.08.28-02.12.25:891][504]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_BlueMidTower1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueMidTower1.uasset" SILENT=true
[2025.08.28-02.12.25:907][504]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_BlueMidTower1
[2025.08.28-02.12.25:907][504]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_BlueMidTower112F328BE429C6A4CB88022B9918F20F2.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueMidTower1.uasset'
[2025.08.28-02.12.25:911][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:912][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:913][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:915][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:915][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:916][504]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.25:929][504]LogFileHelpers: InternalPromptForCheckoutAndSave took 177.431 ms (total: 4.24 sec)
[2025.08.28-02.12.25:929][504]LogTemp: HandleCreateTowerStructures: Tower Blueprint BlueMidTower1 successfully saved at /Game/Auracron/MOBA/Towers/BP_BlueMidTower1
[2025.08.28-02.12.25:929][504]LogTemp: HandleCreateTowerStructures: Created tower BlueMidTower1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.25:929][504]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "BlueMidTower1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.25",
		"location":
		{
			"x": -6000,
			"y": 0,
			"z": 100
		}
	}
}
[2025.08.28-02.12.25:929][504]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 448
[2025.08.28-02.12.25:929][504]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.25:930][504]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.26:005][505]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.26:006][505]LogContentValidation: Enabled validators:
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.26:006][505]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.26:006][505]AssetCheck: /Game/Auracron/MOBA/Towers/BP_BlueMidTower1 Validando ativo
[2025.08.28-02.12.31:359][596]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.31:360][596]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.31:360][596]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.31:460][598]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.31:460][598]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.31:461][598]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "BlueMidTower2", "tower_type": "basic", "location": {"x": -3000.0, "y": 0.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "radiant_golden", "defensive_features": ["energy_shield", "targeting_system"], "visual_effects": ["golden_glow", "energy_particles"]}}}
[2025.08.28-02.12.31:461][598]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.31:462][598]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.31:462][598]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.31:462][598]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.31:517][598]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.08.28-02.12.31:524][598]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_BlueMidTower2 at /Game/Auracron/MOBA/Towers/BP_BlueMidTower2
[2025.08.28-02.12.31:526][598]LogTemp: HandleCreateTowerStructures: Tower BlueMidTower2 spawned in world at location (-3000.0, 0.0, 100.0)
[2025.08.28-02.12.31:526][598]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.31:528][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:645][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:691][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:693][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:695][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:699][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:700][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:705][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:706][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:708][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:709][598]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_BlueMidTower2] ([2] browsable assets)...
[2025.08.28-02.12.31:709][598]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.08.28-02.12.31:709][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:714][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:744][598]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_BlueMidTower2.BP_BlueMidTower2]
[2025.08.28-02.12.31:744][598]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_BlueMidTower2]
[2025.08.28-02.12.31:744][598]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_BlueMidTower2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueMidTower2.uasset" SILENT=true
[2025.08.28-02.12.31:766][598]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_BlueMidTower2
[2025.08.28-02.12.31:767][598]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_BlueMidTower26FE99E184FEF2A21F6742DAC8DDEE86A.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueMidTower2.uasset'
[2025.08.28-02.12.31:772][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:774][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:774][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:777][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:777][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:779][598]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.12.31:792][598]LogFileHelpers: InternalPromptForCheckoutAndSave took 265.786 ms (total: 4.50 sec)
[2025.08.28-02.12.31:792][598]LogTemp: HandleCreateTowerStructures: Tower Blueprint BlueMidTower2 successfully saved at /Game/Auracron/MOBA/Towers/BP_BlueMidTower2
[2025.08.28-02.12.31:792][598]LogTemp: HandleCreateTowerStructures: Created tower BlueMidTower2 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.31:792][598]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "BlueMidTower2",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.31",
		"location":
		{
			"x": -3000,
			"y": 0,
			"z": 100
		}
	}
}
[2025.08.28-02.12.31:792][598]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 448
[2025.08.28-02.12.31:792][598]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.31:794][598]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.31:864][599]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.31:864][599]LogContentValidation: Enabled validators:
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.31:864][599]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.31:864][599]AssetCheck: /Game/Auracron/MOBA/Towers/BP_BlueMidTower2 Validando ativo
[2025.08.28-02.12.42:658][775]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.42:658][775]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.42:658][775]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.42:759][775]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.42:759][775]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.42:759][775]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "BlueBottomTower1", "tower_type": "basic", "location": {"x": -6000.0, "y": 6000.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "radiant_golden", "defensive_features": ["energy_shield", "targeting_system"], "visual_effects": ["golden_glow", "energy_particles"]}}}
[2025.08.28-02.12.42:759][775]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.42:777][775]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.42:777][775]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.42:777][775]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.42:799][775]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.08.28-02.12.42:802][775]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_BlueBottomTower1 at /Game/Auracron/MOBA/Towers/BP_BlueBottomTower1
[2025.08.28-02.12.42:804][775]LogTemp: HandleCreateTowerStructures: Tower BlueBottomTower1 spawned in world at location (-6000.0, 6000.0, 100.0)
[2025.08.28-02.12.42:804][775]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.42:850][775]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_BlueBottomTower1] ([2] browsable assets)...
[2025.08.28-02.12.42:850][775]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.08.28-02.12.42:873][775]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_BlueBottomTower1.BP_BlueBottomTower1]
[2025.08.28-02.12.42:874][775]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_BlueBottomTower1]
[2025.08.28-02.12.42:874][775]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_BlueBottomTower1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueBottomTower1.uasset" SILENT=true
[2025.08.28-02.12.42:885][775]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_BlueBottomTower1
[2025.08.28-02.12.42:885][775]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_BlueBottomTower15D82ED0843B6821D853744B41D269C73.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_BlueBottomTower1.uasset'
[2025.08.28-02.12.42:901][775]LogFileHelpers: InternalPromptForCheckoutAndSave took 98.019 ms (total: 4.60 sec)
[2025.08.28-02.12.42:901][775]LogTemp: HandleCreateTowerStructures: Tower Blueprint BlueBottomTower1 successfully saved at /Game/Auracron/MOBA/Towers/BP_BlueBottomTower1
[2025.08.28-02.12.42:901][775]LogTemp: HandleCreateTowerStructures: Created tower BlueBottomTower1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.42:901][775]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "BlueBottomTower1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.42",
		"location":
		{
			"x": -6000,
			"y": 6000,
			"z": 100
		}
	}
}
[2025.08.28-02.12.42:901][775]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 454
[2025.08.28-02.12.42:901][775]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.42:912][776]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.43:168][776]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.43:168][776]LogContentValidation: Enabled validators:
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.43:168][776]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.43:168][776]AssetCheck: /Game/Auracron/MOBA/Towers/BP_BlueBottomTower1 Validando ativo
[2025.08.28-02.12.48:855][794]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.48:855][794]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.48:855][794]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.12.48:956][794]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.12.48:956][794]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.12.48:956][794]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "RedTopTower1", "tower_type": "basic", "location": {"x": 6000.0, "y": 6000.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_dark", "defensive_features": ["shadow_shield", "targeting_system"], "visual_effects": ["red_glow", "shadow_particles"]}}}
[2025.08.28-02.12.48:956][794]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.12.49:111][794]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.12.49:112][794]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.12.49:112][794]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.12.49:132][794]LogUObjectHash: Compacting FUObjectHashTables data took   0.49ms
[2025.08.28-02.12.49:134][794]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_RedTopTower1 at /Game/Auracron/MOBA/Towers/BP_RedTopTower1
[2025.08.28-02.12.49:135][794]LogTemp: HandleCreateTowerStructures: Tower RedTopTower1 spawned in world at location (6000.0, 6000.0, 100.0)
[2025.08.28-02.12.49:135][794]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.12.49:183][794]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_RedTopTower1] ([2] browsable assets)...
[2025.08.28-02.12.49:183][794]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.08.28-02.12.49:215][794]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_RedTopTower1.BP_RedTopTower1]
[2025.08.28-02.12.49:215][794]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_RedTopTower1]
[2025.08.28-02.12.49:215][794]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_RedTopTower1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_RedTopTower1.uasset" SILENT=true
[2025.08.28-02.12.49:227][794]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_RedTopTower1
[2025.08.28-02.12.49:227][794]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_RedTopTower1C2F997BB45C04BB586BD73B86182DCC6.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_RedTopTower1.uasset'
[2025.08.28-02.12.49:244][794]LogFileHelpers: InternalPromptForCheckoutAndSave took 109.460 ms (total: 4.71 sec)
[2025.08.28-02.12.49:245][794]LogTemp: HandleCreateTowerStructures: Tower Blueprint RedTopTower1 successfully saved at /Game/Auracron/MOBA/Towers/BP_RedTopTower1
[2025.08.28-02.12.49:245][794]LogTemp: HandleCreateTowerStructures: Created tower RedTopTower1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.12.49:245][794]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "RedTopTower1",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.12.49",
		"location":
		{
			"x": 6000,
			"y": 6000,
			"z": 100
		}
	}
}
[2025.08.28-02.12.49:245][794]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 449
[2025.08.28-02.12.49:245][794]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.12.49:253][794]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.12.49:501][795]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.12.49:501][795]LogContentValidation: Enabled validators:
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.12.49:501][795]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.12.49:501][795]AssetCheck: /Game/Auracron/MOBA/Towers/BP_RedTopTower1 Validando ativo
[2025.08.28-02.13.06:084][873]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.13.06:084][873]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.13.06:084][873]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.13.06:184][876]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.13.06:184][876]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.13.06:184][876]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "RedTopTower2", "tower_type": "basic", "location": {"x": 3000.0, "y": 3000.0, "z": 100.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_dark", "defensive_features": ["shadow_shield", "targeting_system"], "visual_effects": ["red_glow", "shadow_particles"]}}}
[2025.08.28-02.13.06:184][876]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
[2025.08.28-02.13.06:185][876]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
[2025.08.28-02.13.06:185][876]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.13.06:185][876]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.13.06:212][876]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.28-02.13.06:217][876]LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_RedTopTower2 at /Game/Auracron/MOBA/Towers/BP_RedTopTower2
[2025.08.28-02.13.06:217][876]LogTemp: HandleCreateTowerStructures: Tower RedTopTower2 spawned in world at location (3000.0, 3000.0, 100.0)
[2025.08.28-02.13.06:219][876]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.28-02.13.06:219][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:317][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:354][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:356][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:356][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:356][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:357][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:358][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:359][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:360][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:360][876]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_RedTopTower2] ([2] browsable assets)...
[2025.08.28-02.13.06:361][876]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.08.28-02.13.06:361][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:362][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:380][876]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_RedTopTower2.BP_RedTopTower2]
[2025.08.28-02.13.06:380][876]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_RedTopTower2]
[2025.08.28-02.13.06:380][876]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_RedTopTower2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_RedTopTower2.uasset" SILENT=true
[2025.08.28-02.13.06:392][876]LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_RedTopTower2
[2025.08.28-02.13.06:392][876]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_RedTopTower2B49E0BC143C019918083C0AD998846F0.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_RedTopTower2.uasset'
[2025.08.28-02.13.06:396][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:397][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:397][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:399][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:399][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:399][876]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.28-02.13.06:411][876]LogFileHelpers: InternalPromptForCheckoutAndSave took 193.114 ms (total: 4.90 sec)
[2025.08.28-02.13.06:411][876]LogTemp: HandleCreateTowerStructures: Tower Blueprint RedTopTower2 successfully saved at /Game/Auracron/MOBA/Towers/BP_RedTopTower2
[2025.08.28-02.13.06:411][876]LogTemp: HandleCreateTowerStructures: Created tower RedTopTower2 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
[2025.08.28-02.13.06:411][876]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_tower_structures",
		"tower_name": "RedTopTower2",
		"tower_type": "basic",
		"layer_index": 0,
		"team_index": 0,
		"tower_height": 500,
		"tower_radius": 100,
		"tower_levels": 3,
		"hierarchical_instancing": true,
		"pcg_generation": false,
		"success": true,
		"timestamp": "2025.08.27-23.13.06",
		"location":
		{
			"x": 3000,
			"y": 3000,
			"z": 100
		}
	}
}
[2025.08.28-02.13.06:411][876]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 449
[2025.08.28-02.13.06:411][876]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.13.06:411][876]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.28-02.13.06:476][877]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.28-02.13.06:476][877]LogContentValidation: Enabled validators:
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.28-02.13.06:476][877]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.28-02.13.06:476][877]AssetCheck: /Game/Auracron/MOBA/Towers/BP_RedTopTower2 Validando ativo
[2025.08.28-02.13.16:891][ 71]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.13.16:891][ 71]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.13.16:891][ 71]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.28-02.13.16:992][ 71]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.28-02.13.16:992][ 71]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.28-02.13.16:992][ 71]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_nexus_architecture", "params": {"nexus_name": "BlueNexus", "location": {"x": -9000.0, "y": 0.0, "z": 100.0}, "team_side": "blue", "nexus_config": {"nexus_size": "large", "defensive_systems": ["energy_barriers", "auto_turrets", "shield_generators"], "energy_effects": ["golden_aura", "radiant_beams", "protective_dome"], "architectural_grandeur": "high"}}}
[2025.08.28-02.13.16:992][ 71]LogTemp: Display: UnrealMCPBridge: Executing command: create_nexus_architecture
[2025.08.28-02.13.17:200][ 71]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_nexus_architecture
[2025.08.28-02.13.17:200][ 71]LogJson: Warning: Field team_index was not found.
[2025.08.28-02.13.17:200][ 71]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.13.17:200][ 71]LogJson: Warning: Field complexity was not found.
[2025.08.28-02.13.17:200][ 71]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.28-02.13.17:202][ 71]LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - BlueNexus with 4 nodes
[2025.08.28-02.13.17:202][ 71]LogTemp: AURACRON: Successfully created PCG component for structure BlueNexus (Layer: 0) with graph Auracron_BlueNexus_Layer0_PCG
[2025.08.28-02.13.17:202][ 71]LogTemp: SetupPCGGeneration: Created PCG component for structure BlueNexus (Layer: 0)
[2025.08.28-02.13.17:202][ 71]LogTemp: CreateRobustTowerStructure: Created tower BlueNexus with 10 levels (Height: 2000.0, HISM: Yes, PCG: Yes)
[2025.08.28-02.13.17:202][ 71]LogTemp: HandleCreateNexusArchitecture: Created nexus BlueNexus for team 0 (Complexity: 5, Height: 2000.0)
[2025.08.28-02.13.17:202][ 71]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_nexus_architecture",
		"nexus_name": "BlueNexus",
		"team_index": 0,
		"complexity": 5,
		"nexus_height": 2000,
		"nexus_radius": 550,
		"nexus_levels": 10,
		"success": true,
		"timestamp": "2025.08.27-23.13.17"
	}
}
[2025.08.28-02.13.17:202][ 71]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 290
[2025.08.28-02.13.17:202][ 71]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.28-02.22.17:783][275]LogUObjectHash: Compacting FUObjectHashTables data took   0.56ms
[2025.08.28-02.22.17:784][275]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto2.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.28-02.22.17:815][275]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/AURACRON_Auto2
[2025.08.28-02.22.17:815][275]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Auto2C7F42B0442FC0979A426D9BF002ADB8C.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto2.umap'
[2025.08.28-02.22.17:816][275]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/AURACRON' took 0.058
[2025.08.28-02.22.17:816][275]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.058
