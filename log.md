LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "PlanicieRadiante_Base",
        "class": "StaticMeshActor",
        "location": [ 0, 0, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 197
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "FirmamentoZephyr_Base", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 3000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "FirmamentoZephyr_Base",
        "class": "StaticMeshActor",
        "location": [ 0, 0, 3000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 197
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AbismoUmbral_Base", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 5000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "AbismoUmbral_Base",
        "class": "StaticMeshActor",
        "location": [ 0, 0, 5000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 193
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "PlanicieRadiante_Terrain", "layer_index": 0, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 0.0}, "heightmap_settings": {"noise_type": "perlin", "frequency": 0.01, "amplitude": 200, "octaves": 4, "lacunarity": 2, "persistence": 0.5}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
LogJson: Warning: Field use_pcg was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_nanite was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_world_partition was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: CreateLayerMaterial: Created material for layer 0 with theme color (R=1.000000,G=0.800000,B=0.200000,A=1.000000)
LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateLayerMaterial: Created material for layer 1 with theme color (R=0.200000,G=0.800000,B=1.000000,A=1.000000)
LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateLayerMaterial: Created material for layer 2 with theme color (R=0.400000,G=0.200000,B=0.800000,A=1.000000)
LogTemp: CreateRobustAuracronLandscape: Created landscape PlanicieRadiante_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for PlanicieRadiante_Terrain
LogTemp: HandleCreateProceduralLandscape: Created landscape PlanicieRadiante_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_procedural_landscape",
        "landscape_name": "PlanicieRadiante_Terrain",
        "use_pcg": false,
        "enable_nanite": false,
        "enable_world_partition": false,
        "components_created": 3,
        "layers_created": 3,
        "success": true,
        "timestamp": "2025.08.27-22.55.31"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 335
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "FirmamentoZephyr_Terrain", "layer_index": 1, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 2000.0}, "heightmap_settings": {"noise_type": "simplex", "frequency": 0.008, "amplitude": 150, "octaves": 3, "lacunarity": 2.2, "persistence": 0.6}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
LogJson: Warning: Field use_pcg was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_nanite was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_world_partition was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created landscape FirmamentoZephyr_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for FirmamentoZephyr_Terrain
LogTemp: HandleCreateProceduralLandscape: Created landscape FirmamentoZephyr_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_procedural_landscape",
        "landscape_name": "FirmamentoZephyr_Terrain",
        "use_pcg": false,
        "enable_nanite": false,
        "enable_world_partition": false,
        "components_created": 3,
        "layers_created": 3,
        "success": true,
        "timestamp": "2025.08.27-22.55.46"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 335
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "AbismoUmbral_Terrain", "layer_index": 2, "size_x": 1009, "size_y": 1009, "scale_x": 178.4, "scale_y": 178.4, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 4000.0}, "heightmap_settings": {"noise_type": "ridged", "frequency": 0.012, "amplitude": 300, "octaves": 5, "lacunarity": 1.8, "persistence": 0.4}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
LogJson: Warning: Field use_pcg was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_nanite was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogJson: Warning: Field enable_world_partition was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created landscape AbismoUmbral_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for AbismoUmbral_Terrain
LogTemp: HandleCreateProceduralLandscape: Created landscape AbismoUmbral_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_procedural_landscape",
        "landscape_name": "AbismoUmbral_Terrain",
        "use_pcg": false,
        "enable_nanite": false,
        "enable_world_partition": false,
        "components_created": 3,
        "layers_created": 3,
        "success": true,
        "timestamp": "2025.08.27-22.55.56"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 331
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "TopLane_PlanicieRadiante", "lane_type": "top", "start_location": {"x": -7000.0, "y": 7000.0, "z": 100.0}, "end_location": {"x": 7000.0, "y": -7000.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 20, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
LogJson: Warning: Field layer_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: GenerateLaneGeometryWithFlow: Generated lane TopLane_PlanicieRadiante with 45 vertices, 64 triangles
LogTemp: CreateRobustProceduralMesh: Created mesh TopLane_PlanicieRadiante with 56 vertices
LogTemp: HandleCreateLaneGeometry: Created lane TopLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_lane_geometry",
        "lane_name": "TopLane_PlanicieRadiante",
        "lane_width": 800,
        "layer_index": 0,
        "lane_points_count": 5,
        "success": true,
        "timestamp": "2025.08.27-22.56.07"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
LogSlate: Window 'Salvar conteúdo' being destroyed
LogFileHelpers: InternalPromptForCheckoutAndSave started...
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
LogUObjectHash: Compacting FUObjectHashTables data took   0.25ms
LogSavePackage: Moving output files for package: /Game/AURACRON
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON53C8CBD8445B9918AFFAB8AABCD69898.tmp' to '../../../../../../Game/AURACRON/Content/AURACRON.umap'
LogFileHelpers: Saving map 'AURACRON' took 0.076
LogFileHelpers: InternalPromptForCheckoutAndSave took 169.408 ms
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/AURACRON Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "MidLane_PlanicieRadiante", "lane_type": "middle", "start_location": {"x": -6500.0, "y": 0.0, "z": 100.0}, "end_location": {"x": 6500.0, "y": 0.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 18, "elevation_curve": "flat", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
LogJson: Warning: Field layer_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: GenerateLaneGeometryWithFlow: Generated lane MidLane_PlanicieRadiante with 45 vertices, 64 triangles
LogTemp: CreateRobustProceduralMesh: Created mesh MidLane_PlanicieRadiante with 56 vertices
LogTemp: HandleCreateLaneGeometry: Created lane MidLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_lane_geometry",
        "lane_name": "MidLane_PlanicieRadiante",
        "lane_width": 800,
        "layer_index": 0,
        "lane_points_count": 5,
        "success": true,
        "timestamp": "2025.08.27-22.56.30"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "BotLane_PlanicieRadiante", "lane_type": "bottom", "start_location": {"x": -7000.0, "y": -7000.0, "z": 100.0}, "end_location": {"x": 7000.0, "y": 7000.0, "z": 100.0}, "lane_width": 800.0, "geometry_settings": {"segments": 19, "elevation_curve": "gentle", "side_barriers": true, "material_zones": ["grass", "stone", "crystal"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
LogJson: Warning: Field layer_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: GenerateLaneGeometryWithFlow: Generated lane BotLane_PlanicieRadiante with 45 vertices, 64 triangles
LogTemp: CreateRobustProceduralMesh: Created mesh BotLane_PlanicieRadiante with 56 vertices
LogTemp: HandleCreateLaneGeometry: Created lane BotLane_PlanicieRadiante for layer 0 (Width: 800.0, Points: 5)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_lane_geometry",
        "lane_name": "BotLane_PlanicieRadiante",
        "lane_width": 800,
        "layer_index": 0,
        "lane_points_count": 5,
        "success": true,
        "timestamp": "2025.08.27-22.57.02"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 257
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T1", "tower_type": "basic", "location": {"x": -5000.0, "y": 5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T1 spawned in world at location (-5000.0, 5000.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.BP_Torre_Azul_Top_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T18A68FE974C533907FA974C8F46C09DA9.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 138.019 ms (total: 307.427 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Top_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.57.12",
        "location":
        {
            "x": -5000,
            "y": 5000,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T2", "tower_type": "advanced", "location": {"x": -3500.0, "y": 3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T2 spawned in world at location (-3500.0, 3500.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.BP_Torre_Azul_Top_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T2D719D30348C088B52A4A78A545AD0CCF.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 114.050 ms (total: 421.478 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Top_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.57.35",
        "location":
        {
            "x": -3500,
            "y": 3500,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Mid_T1", "tower_type": "basic", "location": {"x": -4000.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Mid_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Mid_T1 spawned in world at location (-4000.0, 0.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.BP_Torre_Azul_Mid_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Mid_T1FF6F0E9440FBD5A03178CB951F6AF04E.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 102.878 ms (total: 524.356 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Mid_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Mid_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Mid_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.57.47",
        "location":
        {
            "x": -4000,
            "y": 0,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 452
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Bot_T1", "tower_type": "basic", "location": {"x": -5000.0, "y": -5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["light_aura", "crystal_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Bot_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Bot_T1 spawned in world at location (-5000.0, -5000.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.BP_Torre_Azul_Bot_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Bot_T16BB918784E6E832CEBC2BBA428CCA1D4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 114.562 ms (total: 638.918 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Bot_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Bot_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Bot_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.57.55",
        "location":
        {
            "x": -5000,
            "y": -5000,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 456
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Top_T1", "tower_type": "basic", "location": {"x": 5000.0, "y": -5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.62ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Top_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Top_T1 spawned in world at location (5000.0, -5000.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.BP_Torre_Vermelha_Top_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Top_T1CFF497EA44F77E1EF2EAF1B158EAFB7A.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 122.320 ms (total: 761.239 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Top_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Top_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Top_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.58.04",
        "location":
        {
            "x": 5000,
            "y": -5000,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 459
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Mid_T1", "tower_type": "basic", "location": {"x": 4000.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Mid_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Mid_T1 spawned in world at location (4000.0, 0.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.BP_Torre_Vermelha_Mid_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Mid_T11EA7687B4C3822F0258463BF08C5E295.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 105.041 ms (total: 866.280 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Mid_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Mid_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Mid_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.58.16",
        "location":
        {
            "x": 4000,
            "y": 0,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Bot_T1", "tower_type": "basic", "location": {"x": 5000.0, "y": 5000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 800, "base_radius": 200, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting"], "visual_effects": ["dark_aura", "red_glow"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Bot_T1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Bot_T1 spawned in world at location (5000.0, 5000.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.BP_Torre_Vermelha_Bot_T1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Bot_T11AAABB214DF1028AC0274D85DEF2C7E3.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 124.937 ms (total: 991.217 ms)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Bot_T1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Bot_T1 (Type: basic, Layer: 0, Team: 0, Height: 500.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Bot_T1",
        "tower_type": "basic",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 500,
        "tower_radius": 100,
        "tower_levels": 3,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-22.58.43",
        "location":
        {
            "x": 5000,
            "y": 5000,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_nexus_architecture", "params": {"nexus_name": "Nexus_Azul_PlanicieRadiante", "location": {"x": -8000.0, "y": 0.0, "z": 300.0}, "team_side": "blue", "nexus_config": {"nexus_size": "large", "defensive_systems": ["crystal_barriers", "energy_dome", "auto_repair"], "energy_effects": ["light_pillar", "crystal_resonance", "healing_aura"], "architectural_grandeur": "high"}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_nexus_architecture
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_nexus_architecture
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogJson: Warning: Field complexity was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Nexus_Azul_PlanicieRadiante with 4 nodes
LogTemp: AURACRON: Successfully created PCG component for structure Nexus_Azul_PlanicieRadiante (Layer: 0) with graph Auracron_Nexus_Azul_PlanicieRadiante_Layer0_PCG
LogTemp: SetupPCGGeneration: Created PCG component for structure Nexus_Azul_PlanicieRadiante (Layer: 0)
LogOutputDevice: Warning: Script Stack (0 frames) :
LogStats: FPlatformStackWalk::StackWalkAndDump -  0.125 s
LogOutputDevice: Error: === Handled ensure: ===
LogOutputDevice: Error: Ensure condition failed: MyOwnerWorld  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\Components\ActorComponent.cpp] [Line: 1965] 
LogOutputDevice: Error: Stack: 
LogOutputDevice: Error: [Callstack] 0x00007fff4c909369 UnrealEditor-Engine.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ffedaf99720 UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::CreateRobustTowerStructure() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:670]
LogOutputDevice: Error: [Callstack] 0x00007ffedafa05ba UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCreateNexusArchitecture() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:375]
LogOutputDevice: Error: [Callstack] 0x00007ffedaf9b348 UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCommand() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:90]
LogOutputDevice: Error: [Callstack] 0x00007ffedaf7202d UnrealEditor-UnrealMCP.dll!`UUnrealMCPBridge::ExecuteCommand'::`2'::<lambda_1>::operator()() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp:348]
LogOutputDevice: Error: [Callstack] 0x00007fff5163cd83 UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff5165fb72 UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff5165284f UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff51652ece UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff51c41aa4 UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff51c435bf UnrealEditor-Core.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ffedd2e0b86 UnrealEditor-MassEntityEditor.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff484237cb UnrealEditor-UnrealEd.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff4e272c25 UnrealEditor-Engine.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff489d1ff7 UnrealEditor-UnrealEd.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fff496a5956 UnrealEditor-UnrealEd.dll!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70be99ce4 UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70bebe5ac UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70bebe6ba UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70bec209e UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70bed4e44 UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007ff70bed80fa UnrealEditor.exe!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fffec69e8d7 KERNEL32.DLL!UnknownFunction []
LogOutputDevice: Error: [Callstack] 0x00007fffee5bc34c ntdll.dll!UnknownFunction []
LogStats:                SubmitErrorReport -  0.000 s
LogStats:                    SendNewReport -  0.859 s
LogStats:             FDebug::EnsureFailed -  0.988 s
LogTemp: CreateRobustTowerStructure: Created tower Nexus_Azul_PlanicieRadiante with 10 levels (Height: 2000.0, HISM: Yes, PCG: Yes)
LogTemp: HandleCreateNexusArchitecture: Created nexus Nexus_Azul_PlanicieRadiante for team 0 (Complexity: 5, Height: 2000.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_nexus_architecture",
        "nexus_name": "Nexus_Azul_PlanicieRadiante",
        "team_index": 0,
        "complexity": 5,
        "nexus_height": 2000,
        "nexus_radius": 550,
        "nexus_levels": 10,
        "success": true,
        "timestamp": "2025.08.27-22.59.19"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 308
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_nexus_architecture", "params": {"nexus_name": "Nexus_Vermelho_PlanicieRadiante", "location": {"x": 8000.0, "y": 0.0, "z": 300.0}, "team_side": "red", "nexus_config": {"nexus_size": "large", "defensive_systems": ["shadow_barriers", "dark_dome", "auto_repair"], "energy_effects": ["dark_pillar", "shadow_resonance", "corruption_aura"], "architectural_grandeur": "high"}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_nexus_architecture
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_nexus_architecture
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogJson: Warning: Field complexity was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Nexus_Vermelho_PlanicieRadiante with 4 nodes
LogTemp: AURACRON: Successfully created PCG component for structure Nexus_Vermelho_PlanicieRadiante (Layer: 0) with graph Auracron_Nexus_Vermelho_PlanicieRadiante_Layer0_PCG
LogTemp: SetupPCGGeneration: Created PCG component for structure Nexus_Vermelho_PlanicieRadiante (Layer: 0)
LogTemp: CreateRobustTowerStructure: Created tower Nexus_Vermelho_PlanicieRadiante with 10 levels (Height: 2000.0, HISM: Yes, PCG: Yes)
LogTemp: HandleCreateNexusArchitecture: Created nexus Nexus_Vermelho_PlanicieRadiante for team 0 (Complexity: 5, Height: 2000.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_nexus_architecture",
        "nexus_name": "Nexus_Vermelho_PlanicieRadiante",
        "team_index": 0,
        "complexity": 5,
        "nexus_height": 2000,
        "nexus_radius": 550,
        "nexus_levels": 10,
        "success": true,
        "timestamp": "2025.08.27-22.59.26"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 312
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Pequeno_1", "camp_type": "small", "location": {"x": -3000.0, "y": 2000.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "light_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "light_blessing"}, "respawn_timer": 60}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Pequeno_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Pequeno_1 (Type: small, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Azul_Pequeno_1",
        "camp_type": "small",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-22.59.34"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 227
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Medio_1", "camp_type": "medium", "location": {"x": -2500.0, "y": 3500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "crystal_golem", "count": 1, "level": 3}, {"type": "light_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "crystal_armor"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Medio_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Medio_1 (Type: medium, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Azul_Medio_1",
        "camp_type": "medium",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-22.59.41"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Luz_Azul", "camp_type": "large", "location": {"x": -1500.0, "y": 1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "guardian_of_light", "count": 1, "level": 5}, {"type": "light_sprite", "count": 3, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_da_luz"}, "respawn_timer": 300}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Luz_Azul with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Luz_Azul (Type: large, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Buff_Luz_Azul",
        "camp_type": "large",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-22.59.49"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Crescimento_Azul", "camp_type": "large", "location": {"x": -1500.0, "y": -1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "ancient_treant", "count": 1, "level": 5}, {"type": "nature_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_do_crescimento"}, "respawn_timer": 300}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Crescimento_Azul with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Crescimento_Azul (Type: large, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Buff_Crescimento_Azul",
        "camp_type": "large",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-22.59.55"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 234
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Luz_Vermelho", "camp_type": "large", "location": {"x": 1500.0, "y": -1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "guardian_of_light", "count": 1, "level": 5}, {"type": "light_sprite", "count": 3, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_da_luz"}, "respawn_timer": 300}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Luz_Vermelho with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Luz_Vermelho (Type: large, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Buff_Luz_Vermelho",
        "camp_type": "large",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.00.02"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Buff_Crescimento_Vermelho", "camp_type": "large", "location": {"x": 1500.0, "y": 1500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "ancient_treant", "count": 1, "level": 5}, {"type": "nature_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 300, "experience": 400, "buff": "buff_do_crescimento"}, "respawn_timer": 300}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Buff_Crescimento_Vermelho with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Buff_Crescimento_Vermelho (Type: large, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Buff_Crescimento_Vermelho",
        "camp_type": "large",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.00.08"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 238
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Guardiao_da_Aurora", "camp_type": "epic", "location": {"x": 0.0, "y": 4000.0, "z": 200.0}, "layer_index": 0, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "aurora_guardian", "count": 1, "level": 12}], "reward_systems": {"gold": 1500, "experience": 2000, "buff": "aurora_blessing", "team_buff": true}, "respawn_timer": 420}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: AURACRON: FULLY CONFIGURED PCG for Planície Radiante - Guardiao_da_Aurora with 4 nodes
LogTemp: AURACRON: Successfully created PCG component for structure Guardiao_da_Aurora (Layer: 0) with graph Auracron_Guardiao_da_Aurora_Layer0_PCG
LogTemp: SetupPCGGeneration: Created PCG component for structure Guardiao_da_Aurora (Layer: 0)
LogTemp: CreateRobustTowerStructure: Created tower Guardiao_da_Aurora with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
LogTemp: HandleCreateJungleCamps: Created camp Guardiao_da_Aurora (Type: epic, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Guardiao_da_Aurora",
        "camp_type": "epic",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.00.17"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 225
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_portal_geometry", "params": {"portal_name": "Portal_Top_River", "portal_type": "teleporter", "source_location": {"x": 0.0, "y": 6000.0, "z": 1000.0}, "target_location": {"x": 0.0, "y": 6000.0, "z": 3000.0}, "portal_settings": {"portal_size": 400, "visual_effects": ["energy_spiral", "light_particles", "dimensional_rift"], "activation_method": "proximity", "transition_time": 3}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_portal_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_portal_geometry
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "error",
    "error": "Missing required parameters: portal_name, source_layer, target_layer"
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 107
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_portal_system", "params": {"map_name": "AuracronMainMap", "portals": [{"name": "Portal_Top_River", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": 6000, "z": 1000}, "target_location": {"x": 0, "y": 6000, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Mid_Center", "source_l
ayer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": 0, "z": 1000}, "target_location": {"x": 0, "y": 0, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Bot_River", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 0, "y": -6000, "z": 1000}, "target_location": {"x": 0, "y": -6000, "z": 3000}, "portal_type
": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_NE", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": 4500, "y": 4500, "z": 1000}, "target_location": {"x": 4500, "y": 4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_eff
ects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_NW", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": {"x": -4500, "y": 4500, "z": 1000}, "target_location": {"x": -4500, "y": 4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}, {"name": "Portal_Jungle_SE", "source_layer": "PlanicieRadiante", "target_layer": "FirmamentoZephyr", "source_location": 
{"x": 4500, "y": -4500, "z": 1000}, "target_location": {"x": 4500, "y": -4500, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_spiral", "light_particles"], "audio_effects": ["portal_hum", "dimensional_shift"]}]}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_portal_system
LogTemp: [MapSystem] Creating portal system...
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "error",
    "error": "Map not found: /Game/Maps/AuracronMainMap"
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 80
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Top_River", "type": "STATICMESHACTOR", "location": [0.0, 6000.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Top_River",
        "class": "StaticMeshActor",
        "location": [ 0, 6000, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 195
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Mid_Center", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Mid_Center",
        "class": "StaticMeshActor",
        "location": [ 0, 0, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 193
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Bot_River", "type": "STATICMESHACTOR", "location": [0.0, -6000.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Bot_River",
        "class": "StaticMeshActor",
        "location": [ 0, -6000, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 196
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_NE", "type": "STATICMESHACTOR", "location": [4500.0, 4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Jungle_NE",
        "class": "StaticMeshActor",
        "location": [ 4500, 4500, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 198
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_NW", "type": "STATICMESHACTOR", "location": [-4500.0, 4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Jungle_NW",
        "class": "StaticMeshActor",
        "location": [ -4500, 4500, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 199
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Portal_Jungle_SE", "type": "STATICMESHACTOR", "location": [4500.0, -4500.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Portal_Jungle_SE",
        "class": "StaticMeshActor",
        "location": [ 4500, -4500, 1000 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 199
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_elevator_system", "params": {"map_name": "AuracronMainMap", "elevator_locations": [{"x": 0.0, "y": 7500.0, "z": 0.0}, {"x": 0.0, "y": -7500.0, "z": 0.0}, {"x": 7500.0, "y": 0.0, "z": 0.0}, {"x": -7500.0, "y": 0.0, "z": 0.0}], "capacity": 5, "travel_time": 2.5, "vulnerability_enabled": true}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_elevator_system
LogTemp: [MapSystem] Creating elevator system...
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "error",
    "error": "Map not found: /Game/Maps/AuracronMainMap"
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 80
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Norte", "type": "STATICMESHACTOR", "location": [0.0, 7500.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Elevador_Norte",
        "class": "StaticMeshActor",
        "location": [ 0, 7500, 0 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Sul", "type": "STATICMESHACTOR", "location": [0.0, -7500.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Elevador_Sul",
        "class": "StaticMeshActor",
        "location": [ 0, -7500, 0 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 189
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Leste", "type": "STATICMESHACTOR", "location": [7500.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Elevador_Leste",
        "class": "StaticMeshActor",
        "location": [ 7500, 0, 0 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Elevador_Oeste", "type": "STATICMESHACTOR", "location": [-7500.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Elevador_Oeste",
        "class": "StaticMeshActor",
        "location": [ -7500, 0, 0 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 191
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "Corrente_Vento_Norte", "lane_type": "top", "start_location": {"x": -6000.0, "y": 4000.0, "z": 2100.0}, "end_location": {"x": 6000.0, "y": 4000.0, "z": 2100.0}, "lane_width": 1000.0, "geometry_settings": {"segments": 15, "elevation_curve": "floating", "side_barriers": false, "material_zones": ["wind", "cloud", "ethereal"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
LogJson: Warning: Field layer_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: GenerateLaneGeometryWithFlow: Generated lane Corrente_Vento_Norte with 45 vertices, 64 triangles
LogTemp: CreateRobustProceduralMesh: Created mesh Corrente_Vento_Norte with 56 vertices
LogTemp: HandleCreateLaneGeometry: Created lane Corrente_Vento_Norte for layer 0 (Width: 1000.0, Points: 5)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_lane_geometry",
        "lane_name": "Corrente_Vento_Norte",
        "lane_width": 1000,
        "layer_index": 0,
        "lane_points_count": 5,
        "success": true,
        "timestamp": "2025.08.27-23.01.51"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 254
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_lane_geometry", "params": {"lane_name": "Corrente_Vento_Sul", "lane_type": "bottom", "start_location": {"x": -6000.0, "y": -4000.0, "z": 2100.0}, "end_location": {"x": 6000.0, "y": -4000.0, "z": 2100.0}, "lane_width": 1000.0, "geometry_settings": {"segments": 15, "elevation_curve": "floating", "side_barriers": false, "material_zones": ["wind", "cloud", "ethereal"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_lane_geometry
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_lane_geometry
LogJson: Warning: Field layer_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: GenerateLaneGeometryWithFlow: Generated lane Corrente_Vento_Sul with 45 vertices, 64 triangles
LogTemp: CreateRobustProceduralMesh: Created mesh Corrente_Vento_Sul with 56 vertices
LogTemp: HandleCreateLaneGeometry: Created lane Corrente_Vento_Sul for layer 0 (Width: 1000.0, Points: 5)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_lane_geometry",
        "lane_name": "Corrente_Vento_Sul",
        "lane_width": 1000,
        "layer_index": 0,
        "lane_points_count": 5,
        "success": true,
        "timestamp": "2025.08.27-23.01.58"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 252
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Zephyr_Norte_1", "tower_type": "advanced", "location": {"x": -4000.0, "y": 4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "wind_spire", "defensive_features": ["wind_barrier", "aerial_targeting", "storm_shield"], "visual_effects": ["wind_swirl", "lightning_arcs", "cloud_formation"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Zephyr_Norte_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Zephyr_Norte_1 spawned in world at location (-4000.0, 4000.0, 2200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.BP_Torre_Azul_Zephyr_Norte_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Zephyr_Norte_170C4765843CC4DF0DDBB3D915DCE106B.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 139.807 ms (total: 1.13 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Zephyr_Norte_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Zephyr_Norte_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Zephyr_Norte_1",
        "tower_type": "advanced",
        "layer_index": 1,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.02.05",
        "location":
        {
            "x": -4000,
            "y": 4000,
            "z": 2200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 466
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Norte_1 Validando ativo
LogUObjectHash: Compacting FUObjectHashTables data took   1.20ms
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Zephyr_Sul_1", "tower_type": "advanced", "location": {"x": -4000.0, "y": -4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "wind_spire", "defensive_features": ["wind_barrier", "aerial_targeting", "storm_shield"], "visual_effects": ["wind_swirl", "lightning_arcs", "cloud_formation"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Zephyr_Sul_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Zephyr_Sul_1 spawned in world at location (-4000.0, -4000.0, 2200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.BP_Torre_Azul_Zephyr_Sul_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Zephyr_Sul_199D032934C3D3FBC142B7D8A69E20057.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 148.098 ms (total: 1.27 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Zephyr_Sul_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Zephyr_Sul_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Zephyr_Sul_1",
        "tower_type": "advanced",
        "layer_index": 1,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.02.11",
        "location":
        {
            "x": -4000,
            "y": -4000,
            "z": 2200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 465
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSlate: Window 'Salvar conteúdo' being destroyed
LogFileHelpers: InternalPromptForCheckoutAndSave started...
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
LogUObjectHash: Compacting FUObjectHashTables data took   0.41ms
LogSavePackage: Moving output files for package: /Game/AURACRON
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON9861061C415371359E482C94211094D3.tmp' to '../../../../../../Game/AURACRON/Content/AURACRON.umap'
LogFileHelpers: Saving map 'AURACRON' took 0.076
LogFileHelpers: InternalPromptForCheckoutAndSave took 150.735 ms (total: 1.42 sec)
LogContentValidation: Display: Starting to validate 2 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Zephyr_Sul_1 Validando ativo
AssetCheck: /Game/AURACRON Validando ativo
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Zephyr_Norte_1", "tower_type": "advanced", "location": {"x": 4000.0, "y": 4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "storm_spire", "defensive_features": ["dark_wind_barrier", "aerial_targeting", "shadow_storm"], "visual_effects": ["dark_wind_swirl", "red_lightning", "storm_clouds"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Zephyr_Norte_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Zephyr_Norte_1 spawned in world at location (4000.0, 4000.0, 2200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1.BP_Torre_Vermelha_Zephyr_Norte_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1]
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Zephyr_Norte_1FC2F20844C5B7CE0D0BC95943D7C8D2E.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 118.355 ms (total: 1.54 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Zephyr_Norte_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Zephyr_Norte_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Zephyr_Norte_1",
        "tower_type": "advanced",
        "layer_index": 1,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.02.17",
        "location":
        {
            "x": 4000,
            "y": 4000,
            "z": 2200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 469
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Norte_1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Zephyr_Sul_1", "tower_type": "advanced", "location": {"x": 4000.0, "y": -4000.0, "z": 2200.0}, "layer_index": 1, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "storm_spire", "defensive_features": ["dark_wind_barrier", "aerial_targeting", "shadow_storm"], "visual_effects": ["dark_wind_swirl", "red_lightning", "storm_clouds"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.60ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Zephyr_Sul_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Zephyr_Sul_1 spawned in world at location (4000.0, -4000.0, 2200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1.BP_Torre_Vermelha_Zephyr_Sul_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1]
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Zephyr_Sul_1F638389B4C3FC6B7AADB9BA44D5BE1C2.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 119.706 ms (total: 1.66 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Zephyr_Sul_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Zephyr_Sul_1 (Type: advanced, Layer: 1, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Zephyr_Sul_1",
        "tower_type": "advanced",
        "layer_index": 1,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.02.23",
        "location":
        {
            "x": 4000,
            "y": -4000,
            "z": 2200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 468
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Zephyr_Sul_1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Senhor_dos_Ventos", "camp_type": "epic", "location": {"x": 0.0, "y": 0.0, "z": 2300.0}, "layer_index": 1, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "wind_lord", "count": 1, "level": 15}], "reward_systems": {"gold": 2000, "experience": 2500, "buff": "wind_mastery", "team_buff": true}, "respawn_timer": 480}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogPCG: Error: From node AttributeFilter_0 does not have the Out label
LogTemp: AURACRON: FULLY CONFIGURED PCG for Firmamento Zephyr - Senhor_dos_Ventos with 5 nodes
LogTemp: AURACRON: Successfully created PCG component for structure Senhor_dos_Ventos (Layer: 1) with graph Auracron_Senhor_dos_Ventos_Layer1_PCG
LogTemp: SetupPCGGeneration: Created PCG component for structure Senhor_dos_Ventos (Layer: 1)
LogTemp: CreateRobustTowerStructure: Created tower Senhor_dos_Ventos with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
LogTemp: HandleCreateJungleCamps: Created camp Senhor_dos_Ventos (Type: epic, Layer: 1)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Senhor_dos_Ventos",
        "camp_type": "epic",
        "layer_index": 1,
        "success": true,
        "timestamp": "2025.08.27-23.02.32"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 224
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Azul_1", "camp_type": "medium", "location": {"x": -3000.0, "y": 2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "storm_elemental", "count": 1, "level": 4}, {"type": "wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "storm_speed"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Azul_1 (Type: medium, Layer: 1)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Tempestade_Azul_1",
        "camp_type": "medium",
        "layer_index": 1,
        "success": true,
        "timestamp": "2025.08.27-23.02.41"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_structures", "params": {"structure_name": "Labirinto_Umbral_Centro", "structure_type": "monster_den", "location": {"x": 0.0, "y": 0.0, "z": 4200.0}, "layer_index": 2, "structure_settings": {"size_variation": "large", "detail_level": "high", "vegetation": false, "defensive_elements": ["shadow_walls", "umbral_traps", "stealth_zones"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_structures
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_jungle_structures
LogJson: Warning: Field complexity was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogTemp: CreateRobustProceduralMesh: Created mesh JungleStructure_monster_den_L2 with 56 vertices
LogTemp: HandleCreateJungleStructures: Created structure monster_den at layer 2 (Complexity: 3)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_structures",
        "structure_type": "monster_den",
        "layer_index": 2,
        "complexity": 3,
        "location":
        {
            "x": 0,
            "y": 0,
            "z": 4200
        },
        "success": true,
        "timestamp": "2025.08.27-23.02.49"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 288
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Umbral_1", "tower_type": "nexus", "location": {"x": -3000.0, "y": 3000.0, "z": 4300.0}, "layer_index": 2, "tower_config": {"height": 1200, "base_radius": 400, "architectural_style": "shadow_obelisk", "defensive_features": ["umbral_shield", "stealth_detection", "shadow_strike"], "visual_effects": ["dark_aura", "shadow_tendrils", "void_energy"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Umbral_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Umbral_1 spawned in world at location (-3000.0, 3000.0, 4300.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.BP_Torre_Azul_Umbral_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Umbral_1312717C048AA6B84A0BAA582DE1B47FE.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 126.372 ms (total: 1.79 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Umbral_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Umbral_1 (Type: nexus, Layer: 2, Team: 0, Height: 1200.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Umbral_1",
        "tower_type": "nexus",
        "layer_index": 2,
        "team_index": 0,
        "tower_height": 1200,
        "tower_radius": 200,
        "tower_levels": 7,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.02.55",
        "location":
        {
            "x": -3000,
            "y": 3000,
            "z": 4300
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Umbral_1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Umbral_1", "tower_type": "nexus", "location": {"x": 3000.0, "y": -3000.0, "z": 4300.0}, "layer_index": 2, "tower_config": {"height": 1200, "base_radius": 400, "architectural_style": "void_obelisk", "defensive_features": ["void_shield", "stealth_detection", "corruption_strike"], "visual_effects": ["crimson_aura", "void_tendrils", "dark_energy"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Umbral_1 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Umbral_1 spawned in world at location (3000.0, -3000.0, 4300.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.BP_Torre_Vermelha_Umbral_1]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Umbral_1D1AEE9DC442A7CF82BF2C699BF383B2A.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 109.677 ms (total: 1.90 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Umbral_1 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Umbral_1 (Type: nexus, Layer: 2, Team: 0, Height: 1200.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Umbral_1",
        "tower_type": "nexus",
        "layer_index": 2,
        "team_index": 0,
        "tower_height": 1200,
        "tower_radius": 200,
        "tower_levels": 7,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.03.01",
        "location":
        {
            "x": 3000,
            "y": -3000,
            "z": 4300
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 462
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Umbral_1 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Arqui_Sombra", "camp_type": "epic", "location": {"x": 0.0, "y": 0.0, "z": 4400.0}, "layer_index": 2, "camp_config": {"camp_size": "epic", "monster_spawns": [{"type": "arch_shadow", "count": 1, "level": 20}], "reward_systems": {"gold": 3000, "experience": 4000, "buff": "umbral_dominion", "team_buff": true}, "respawn_timer": 600}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogPCG: Error: From node AttributeFilter_0 does not have the Out label
LogTemp: AURACRON: FULLY CONFIGURED PCG for Abismo Umbral - Arqui_Sombra with 5 nodes
LogTemp: AURACRON: Successfully created PCG component for structure Arqui_Sombra (Layer: 2) with graph Auracron_Arqui_Sombra_Layer2_PCG
LogTemp: SetupPCGGeneration: Created PCG component for structure Arqui_Sombra (Layer: 2)
LogTemp: CreateRobustTowerStructure: Created tower Arqui_Sombra with 2 levels (Height: 400.0, HISM: Yes, PCG: Yes)
LogTemp: HandleCreateJungleCamps: Created camp Arqui_Sombra (Type: epic, Layer: 2)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Arqui_Sombra",
        "camp_type": "epic",
        "layer_index": 2,
        "success": true,
        "timestamp": "2025.08.27-23.03.08"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 219
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_PlanicieRadiante_Terreno", "layer_index": 0, "material_type": "terrain", "material_properties": {"base_color": [0.4, 0.8, 0.3, 1], "metallic": 0.1, "roughness": 0.7, "normal_intensity": 1.2, "emissive_color": [0.2, 0.4, 0.1, 1], "opacity": 1}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno] ([1] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.Material_PlanicieRadiante_Terreno]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_PlanicieRadiante_Terren8CD0F0B744EEE875FF8A7599869D01F2.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 158.687 ms (total: 2.06 sec)
LogTemp: CreateRobustMaterial: Successfully created and saved material Material_PlanicieRadiante_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance] ([1] browsable assets)...
OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.Material_PlanicieRadiante_Terreno_Instance]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_PlanicieRadiante_Terren839CADB24656E4E2FB8F6DB1B9F671A4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 95.159 ms (total: 2.15 sec)
LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_PlanicieRadiante_Terreno_Instance (Layer: 0, Saved: Yes, Path: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance)
LogTemp: HandleCreateLayerMaterials: Created material Material_PlanicieRadiante_Terreno for layer 0 (Type: terrain, Nanite: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_layer_materials",
        "material_name": "Material_PlanicieRadiante_Terreno",
        "material_type": "terrain",
        "layer_index": 0,
        "nanite_enabled": true,
        "material_layers_enabled": true,
        "success": true,
        "material_asset_path": "/Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno",
        "instance_asset_path": "/Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance",
        "material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno.uasset",
        "instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance.uasset",
        "material_saved_to_disk": true,
        "instance_saved_to_disk": true,
        "files_created": true,
        "timestamp": "2025.08.27-23.03.18",
        "color_scheme":
        {
            "primary_color": "(R=1.000000,G=0.800000,B=0.200000,A=1.000000)",
            "secondary_color": "(R=0.200000,G=0.800000,B=0.300000,A=1.000000)",
            "accent_color": "(R=1.000000,G=1.000000,B=0.800000,A=1.000000)"
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1151
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 2 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/Materials/Layer0/Material_PlanicieRadiante_Terreno Validando ativo
AssetCheck: /Game/Auracron/Materials/Layer0/Instances/Material_PlanicieRadiante_Terreno_Instance Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_FirmamentoZephyr_Terreno", "layer_index": 1, "material_type": "terrain", "material_properties": {"base_color": [0.6, 0.8, 1, 1], "metallic": 0.3, "roughness": 0.4, "normal_intensity": 0.8, "emissive_color": [0.4, 0.6, 0.9, 1], "opacity": 0.8}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno] ([1] browsable assets)...
OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.Material_FirmamentoZephyr_Terreno]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_FirmamentoZephyr_Terren597303D4489CAA5967C7C3928E2847D5.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 132.348 ms (total: 2.29 sec)
LogTemp: CreateRobustMaterial: Successfully created and saved material Material_FirmamentoZephyr_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance] ([1] browsable assets)...
OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.Material_FirmamentoZephyr_Terreno_Instance]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_FirmamentoZephyr_Terren9B0402834E3F6174A43094A7A06D1C6F.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 124.950 ms (total: 2.41 sec)
LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_FirmamentoZephyr_Terreno_Instance (Layer: 1, Saved: Yes, Path: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance)
LogTemp: HandleCreateLayerMaterials: Created material Material_FirmamentoZephyr_Terreno for layer 1 (Type: terrain, Nanite: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_layer_materials",
        "material_name": "Material_FirmamentoZephyr_Terreno",
        "material_type": "terrain",
        "layer_index": 1,
        "nanite_enabled": true,
        "material_layers_enabled": true,
        "success": true,
        "material_asset_path": "/Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno",
        "instance_asset_path": "/Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance",
        "material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno.uasset",
        "instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance.uasset",
        "material_saved_to_disk": true,
        "instance_saved_to_disk": true,
        "files_created": true,
        "timestamp": "2025.08.27-23.03.27",
        "color_scheme":
        {
            "primary_color": "(R=0.200000,G=0.600000,B=1.000000,A=1.000000)",
            "secondary_color": "(R=0.900000,G=0.900000,B=1.000000,A=1.000000)",
            "accent_color": "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)"
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1151
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 2 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/Materials/Layer1/Material_FirmamentoZephyr_Terreno Validando ativo
AssetCheck: /Game/Auracron/Materials/Layer1/Instances/Material_FirmamentoZephyr_Terreno_Instance Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_AbismoUmbral_Terreno", "layer_index": 2, "material_type": "terrain", "material_properties": {"base_color": [0.2, 0.1, 0.3, 1], "metallic": 0.8, "roughness": 0.3, "normal_intensity": 1.5, "emissive_color": [0.4, 0.1, 0.6, 1], "opacity": 1}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno] ([1] browsable assets)...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.Material_AbismoUmbral_Terreno]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_AbismoUmbral_TerrenoAFF425FE45C06BF1A8D515A86A7CAFA7.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 238.474 ms (total: 2.65 sec)
LogTemp: CreateRobustMaterial: Successfully created and saved material Material_AbismoUmbral_Terreno (Nanite: Yes, Saved: Yes, Path: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance] ([1] browsable assets)...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.Material_AbismoUmbral_Terreno_Instance]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance]
LogSavePackage: Moving output files for package: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_AbismoUmbral_Terreno_InA302C9CC443CC4461EA6358C894BB6A4.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 113.480 ms (total: 2.76 sec)
LogTemp: CreateNaniteMaterialInstance: Successfully created and saved instance Material_AbismoUmbral_Terreno_Instance (Layer: 2, Saved: Yes, Path: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance)
LogTemp: HandleCreateLayerMaterials: Created material Material_AbismoUmbral_Terreno for layer 2 (Type: terrain, Nanite: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_layer_materials",
        "material_name": "Material_AbismoUmbral_Terreno",
        "material_type": "terrain",
        "layer_index": 2,
        "nanite_enabled": true,
        "material_layers_enabled": true,
        "success": true,
        "material_asset_path": "/Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno",
        "instance_asset_path": "/Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance",
        "material_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno.uasset",
        "instance_disk_path": "../../../../../../Game/AURACRON/Content/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance.uasset",
        "material_saved_to_disk": true,
        "instance_saved_to_disk": true,
        "files_created": true,
        "timestamp": "2025.08.27-23.03.34",
        "color_scheme":
        {
            "primary_color": "(R=0.400000,G=0.100000,B=0.800000,A=1.000000)",
            "secondary_color": "(R=0.100000,G=0.100000,B=0.200000,A=1.000000)",
            "accent_color": "(R=0.800000,G=0.200000,B=1.000000,A=1.000000)"
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1131
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 2 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/Materials/Layer2/Material_AbismoUmbral_Terreno Validando ativo
AssetCheck: /Game/Auracron/Materials/Layer2/Instances/Material_AbismoUmbral_Terreno_Instance Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_PlanicieRadiante_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 1500.0}, "layer_index": 0, "intensity": 5, "color": {"r": 1, "g": 0.9, "b": 0.7, "a": 1}, "attenuation_radius": 20000, "cast_shadows": true, "use_lumen": true}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
LogTemp: CreateRobustLighting: Created light Luz_PlanicieRadiante_Principal (Type: directional, Intensity: 5.0, Lumen: Yes, Shadows: Yes)
LogTemp: HandleCreateDynamicLighting: Created light Luz_PlanicieRadiante_Principal (Type: directional, Layer: 0, Intensity: 5.0, Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_dynamic_lighting",
        "light_name": "Luz_PlanicieRadiante_Principal",
        "light_type": "directional",
        "layer_index": 0,
        "intensity": 5,
        "use_lumen": true,
        "cast_shadows": true,
        "success": true,
        "timestamp": "2025.08.27-23.03.43",
        "location":
        {
            "x": 0,
            "y": 0,
            "z": 1500
        },
        "color":
        {
            "r": 1,
            "g": 0.89999997615814209,
            "b": 0.69999998807907104,
            "a": 1
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 486
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_FirmamentoZephyr_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 3500.0}, "layer_index": 1, "intensity": 4, "color": {"r": 0.7, "g": 0.9, "b": 1, "a": 1}, "attenuation_radius": 18000, "cast_shadows": true, "use_lumen": true}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
LogTemp: CreateRobustLighting: Created light Luz_FirmamentoZephyr_Principal (Type: directional, Intensity: 4.0, Lumen: Yes, Shadows: Yes)
LogTemp: HandleCreateDynamicLighting: Created light Luz_FirmamentoZephyr_Principal (Type: directional, Layer: 1, Intensity: 4.0, Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_dynamic_lighting",
        "light_name": "Luz_FirmamentoZephyr_Principal",
        "light_type": "directional",
        "layer_index": 1,
        "intensity": 4,
        "use_lumen": true,
        "cast_shadows": true,
        "success": true,
        "timestamp": "2025.08.27-23.03.49",
        "location":
        {
            "x": 0,
            "y": 0,
            "z": 3500
        },
        "color":
        {
            "r": 0.69999998807907104,
            "g": 0.89999997615814209,
            "b": 1,
            "a": 1
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 486
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSlate: Took 0.000223 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Luz_AbismoUmbral_Principal", "light_type": "directional", "location": {"x": 0.0, "y": 0.0, "z": 5500.0}, "layer_index": 2, "intensity": 2, "color": {"r": 0.4, "g": 0.2, "b": 0.8, "a": 1}, "attenuation_radius": 15000, "cast_shadows": true, "use_lumen": true}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
LogTemp: CreateRobustLighting: Created light Luz_AbismoUmbral_Principal (Type: directional, Intensity: 2.0, Lumen: Yes, Shadows: Yes)
LogTemp: HandleCreateDynamicLighting: Created light Luz_AbismoUmbral_Principal (Type: directional, Layer: 2, Intensity: 2.0, Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_dynamic_lighting",
        "light_name": "Luz_AbismoUmbral_Principal",
        "light_type": "directional",
        "layer_index": 2,
        "intensity": 2,
        "use_lumen": true,
        "cast_shadows": true,
        "success": true,
        "timestamp": "2025.08.27-23.03.55",
        "location":
        {
            "x": 0,
            "y": 0,
            "z": 5500
        },
        "color":
        {
            "r": 0.40000000596046448,
            "g": 0.20000000298023224,
            "b": 0.80000001192092896,
            "a": 1
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 500
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_PlanicieRadiante_Cristais", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 1200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 2, "y": 2, "z": 2}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
LogJson: Warning: Field niagara_system was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
LogStreaming: Warning: LoadPackage: SkipPackage: /Engine/VFX/P_Environmental (0xEE3B0B0271E4E611) - The package to load does not exist on disk or in the loader
LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_PlanicieRadiante_Cristais using mesh /Engine/BasicShapes/Plane
LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_PlanicieRadiante_Cristais (Type: environmental, System: , Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "setup_niagara_effects",
        "effect_name": "Efeito_PlanicieRadiante_Cristais",
        "effect_type": "environmental",
        "niagara_system": "",
        "auto_activate": true,
        "success": true,
        "timestamp": "2025.08.27-23.04.02"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 284
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_FirmamentoZephyr_Ventos", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 3200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 3, "y": 3, "z": 1.5}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
LogJson: Warning: Field niagara_system was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogStreaming: Display: FlushAsyncLoading(479): 1 QueuedPackages, 0 AsyncPackages
LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_FirmamentoZephyr_Ventos using mesh /Engine/BasicShapes/Plane
LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_FirmamentoZephyr_Ventos (Type: environmental, System: , Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "setup_niagara_effects",
        "effect_name": "Efeito_FirmamentoZephyr_Ventos",
        "effect_type": "environmental",
        "niagara_system": "",
        "auto_activate": true,
        "success": true,
        "timestamp": "2025.08.27-23.04.08"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 282
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_niagara_effects", "params": {"effect_name": "Efeito_AbismoUmbral_Sombras", "effect_type": "environmental", "location": {"x": 0.0, "y": 0.0, "z": 5200.0}, "auto_activate": true, "auto_destroy": false, "scale": {"x": 2.5, "y": 2.5, "z": 2}, "rotation": {"pitch": 0, "yaw": 0, "roll": 0}}}
LogTemp: Display: UnrealMCPBridge: Executing command: setup_niagara_effects
LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: setup_niagara_effects
LogJson: Warning: Field niagara_system was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogStreaming: Display: FlushAsyncLoading(480): 1 QueuedPackages, 0 AsyncPackages
LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'ParticleSystem Nenhum./Engine/VFX/P_Environmental'
LogTemp: SetupNiagaraParticleSystem: Created basic visual effect Efeito_AbismoUmbral_Sombras using mesh /Engine/BasicShapes/Plane
LogTemp: HandleSetupNiagaraEffects: Setup Niagara Efeito_AbismoUmbral_Sombras (Type: environmental, System: , Success: Yes)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "setup_niagara_effects",
        "effect_name": "Efeito_AbismoUmbral_Sombras",
        "effect_type": "environmental",
        "niagara_system": "",
        "auto_activate": true,
        "success": true,
        "timestamp": "2025.08.27-23.04.14"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 279
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_collision_profiles", "params": {"profile_system_name": "AuracronCollisionSystem", "layer_profiles": [{"layer_name": "PlanicieRadiante", "collision_enabled": "collision_enabled", "object_type": "RadianteLayer", "collision_responses": {"RadianteLayer": "block", "ZephyrLayer": "ignore", "UmbralLayer": "ignore", "VerticalConnector": "overlap"}}, {"layer_name": "FirmamentoZephyr", "collision_enabled": "collision_enabled", "object_type": "ZephyrLayer", "collision_responses": {"RadianteLayer": "ignore", "ZephyrLayer": "block", "Umb
ralLayer": "ignore", "VerticalConnector": "overlap"}}, {"layer_name": "AbismoUmbral", "collision_enabled": "collision_enabled", "object_type": "UmbralLayer", "collision_responses": {"RadianteLayer": "ignore", "ZephyrLayer": "ignore", "UmbralLayer": "block", "VerticalConnector": "overlap"}}], "collision_channels": [{"channel_name": "RadianteLayer", "channel_type": "WorldStatic"}, {"channel_name": "ZephyrLayer", "channel_type": "WorldStatic"}, {"channel_name": "UmbralLayer", "channel_type": "WorldStatic"}, {"channel_name": "VerticalConnector", "channel_type": "WorldDynamic"}], "chaos_settings": 
{"enable_chaos": true, "solver_iterations": 8, "collision_margin": 0.1, "sleep_threshold": 0.05}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_collision_profiles
LogTemp: FUnrealMCPCollisionCommands::HandleCommand - Processing: create_layer_collision_profiles
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer0_Profile for layer 0 (PlanicieRadiante) with collision enabled: 3
LogTemp: Layer Collision Profiles: Created profile Layer0_Profile for layer 0 (PlanicieRadiante)
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer1_Profile for layer 1 (FirmamentoZephyr) with collision enabled: 3
LogTemp: Layer Collision Profiles: Created profile Layer1_Profile for layer 1 (FirmamentoZephyr)
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogJson: Warning: Field profile_name was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'String'.
LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer2_Profile for layer 2 (AbismoUmbral) with collision enabled: 3
LogTemp: Layer Collision Profiles: Created profile Layer2_Profile for layer 2 (AbismoUmbral)
LogTemp: Layer Collision Profiles: Created custom channel RadianteLayer (WorldStatic) mapped to channel 14
LogTemp: Layer Collision Profiles: Created custom channel ZephyrLayer (WorldStatic) mapped to channel 14
LogTemp: Layer Collision Profiles: Created custom channel UmbralLayer (WorldStatic) mapped to channel 14
LogTemp: Layer Collision Profiles: Created custom channel VerticalConnector (WorldDynamic) mapped to channel 14
LogJson: Warning: Field enable_ccd was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 0 (Iterations: 10.000000, Margin: 0.050000, CCD: No)
LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 0
LogJson: Warning: Field enable_ccd was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 1 (Iterations: 6.000000, Margin: 0.200000, CCD: No)
LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 1
LogJson: Warning: Field enable_ccd was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 2 (Iterations: 12.000000, Margin: 0.030000, CCD: Yes)
LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 2
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Collision/Profiles/AuracronCollisionSystem.AuracronCollisionSystem' could not be found in the Asset Registry.
LogTemp: Layer Collision Profiles system created: AuracronCollisionSystem (Profiles: 3, Layers: 3, Saved: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "profile_system_name": "AuracronCollisionSystem",
        "package_path": "/Game/Auracron/Collision/Profiles/AuracronCollisionSystem",
        "profiles_created": 3,
        "layers_configured": 3,
        "saved_to_disk": false,
        "config_path": "../../../../../../Game/AURACRON/Content/Auracron/Collision/Profiles/AuracronCollisionSystem_Config.json",
        "configuration":
        {
            "profile_system_name": "AuracronCollisionSystem",
            "layer_profiles": [
                {
                    "layer_name": "PlanicieRadiante",
                    "collision_enabled": "collision_enabled",
                    "object_type": "RadianteLayer",
                    "collision_responses":
                    {
                        "RadianteLayer": "block",
                        "ZephyrLayer": "ignore",
                        "UmbralLayer": "ignore",
                        "VerticalConnector": "overlap"
                    }
                },
                {
                    "layer_name": "FirmamentoZephyr",
                    "collision_enabled": "collision_enabled",
                    "object_type": "ZephyrLayer",
                    "collision_responses":
                    {
                        "RadianteLayer": "ignore",
                        "ZephyrLayer": "block",
                        "UmbralLayer": "ignore",
                        "VerticalConnector": "overlap"
                    }
                },
                {
                    "layer_name": "AbismoUmbral",
                    "collision_enabled": "collision_enabled",
                    "object_type": "UmbralLayer",
                    "collision_responses":
                    {
                        "RadianteLayer": "ignore",
                        "ZephyrLayer": "ignore",
                        "UmbralLayer": "block",
                        "VerticalConnector": "overlap"
                    }
                }
            ],
            "profiles_created": 3
        },
        "created_profiles": [
            {
                "layer_index": 0,
                "profile_name": "Layer0_Profile"
            },
            {
                "layer_index": 1,
                "profile_name": "Layer1_Profile"
            },
            {
                "layer_index": 2,
                "profile_name": "Layer2_Profile"
            }
        ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1732
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_pathfinding", "params": {"system_name": "AuracronPathfindingSystem", "layer_count": 3, "layer_heights": [1000.0, 3000.0, 5000.0], "transition_costs": {"portal_primary": 2.0, "elevator_mystic": 3.0, "bridge_dimensional": 1.5, "emergency_exit": 4.0}, "heuristic_settings": {"base_weight": 1, "vertical_penalty": 1.5, "layer_preference": 1.2, "distance_multiplier": 1}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_pathfinding
LogTemp: Multilayer Pathfinding: Set transition cost portal_primary = 2.000000
LogTemp: Multilayer Pathfinding: Set transition cost elevator_mystic = 3.000000
LogTemp: Multilayer Pathfinding: Set transition cost bridge_dimensional = 1.500000
LogTemp: Multilayer Pathfinding: Set transition cost emergency_exit = 4.000000
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Pathfinding/AuracronPathfindingSystem.AuracronPathfindingSystem' could not be found in the Asset Registry.
LogTemp: Multilayer Pathfinding system created and saved: /Game/Auracron/Pathfinding/AuracronPathfindingSystem (Layers: 3, Saved: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "system_name": "AuracronPathfindingSystem",
        "package_path": "/Game/Auracron/Pathfinding/AuracronPathfindingSystem",
        "layer_count": 3,
        "saved_to_disk": false,
        "full_path": "../../../../../../Game/AURACRON/Content/Auracron/Pathfinding/AuracronPathfindingSystem.uasset",
        "layer_configuration": [
            {
                "layer_index": 0,
                "height": 1000,
                "navigation_data_available": false
            },
            {
                "layer_index": 1,
                "height": 3000,
                "navigation_data_available": false
            },
            {
                "layer_index": 2,
                "height": 5000,
                "navigation_data_available": false
            }
        ],
        "transition_costs":
        {
            "portal_primary": 2,
            "elevator_mystic": 3,
            "bridge_dimensional": 1.5,
            "emergency_exit": 4
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 793
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_fog_of_war", "params": {"system_name": "AuracronFogOfWarSystem", "update_frequency": 0.1, "performance_mode": "high", "layer_configs": [{"layer_name": "PlanicieRadiante", "fog_density": 0.3, "visibility_range": 1200, "vertical_range": 400, "fog_color": [0.8, 0.9, 0.7, 0.5]}, {"layer_name": "FirmamentoZephyr", "fog_density": 0.5, "visibility_range": 1000, "vertical_range": 600, "fog_color": [0.7, 0.8, 1, 0.6]}, {"layer_name": "AbismoUmbral", "fog_density": 0.8, "visibility_range": 800, "vertical_range": 300, "fog_color":
 [0.3, 0.2, 0.5, 0.8]}], "visibility_ranges": {"horizontal_same_layer": 1200.0, "vertical_adjacent": 400.0, "ward_horizontal": 1600.0, "ward_vertical": 600.0}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_fog_of_war
LogTemp: Created fog parameter collection: AuracronFogOfWarSystem_FogParams with 3 layers
LogTemp: Multilayer Fog of War: Created fog actor for layer 0 at height 1000.000000
LogTemp: Multilayer Fog of War: Created fog actor for layer 1 at height 3000.000000
LogTemp: Multilayer Fog of War: Created fog actor for layer 2 at height 5000.000000
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem.AuracronFogOfWarSystem' could not be found in the Asset Registry.
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams] ([1] browsable assets)...
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams]
LogSavePackage: Moving output files for package: /Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AuracronFogOfWarSystem_FogParamsAC489D63413809966ECCDBA6CFBEEA70.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 81.883 ms (total: 2.84 sec)
LogTemp: Multilayer Fog of War system created: AuracronFogOfWarSystem (Layers: 3, Performance: high, Saved: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "system_name": "AuracronFogOfWarSystem",
        "package_path": "/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem",
        "parameter_collection_path": "/Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams",
        "layer_count": 3,
        "performance_mode": "high",
        "update_frequency": 0.05000000074505806,
        "saved_to_disk": false,
        "full_path": "../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem.uasset",
        "fog_actors": [
            {
                "layer_index": 0,
                "actor_name": "FogOfWar_Layer_0_AuracronFogOfWarSystem",
                "height": 1000
            },
            {
                "layer_index": 1,
                "actor_name": "FogOfWar_Layer_1_AuracronFogOfWarSystem",
                "height": 3000
            },
            {
                "layer_index": 2,
                "actor_name": "FogOfWar_Layer_2_AuracronFogOfWarSystem",
                "height": 5000
            }
        ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 880
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/Vision/FogOfWar/AuracronFogOfWarSystem_FogParams Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_3d_heatmap_system", "params": {"heatmap_system_name": "AuracronAnalyticsSystem", "tracking_categories": ["movement", "combat", "deaths", "objectives", "layer_transitions", "portal_usage", "elevator_usage"], "layer_configurations": [{"layer_name": "PlanicieRadiante", "resolution": 64, "tracking_intensity": "high", "data_retention": 7200}, {"layer_name": "FirmamentoZephyr", "resolution": 48, "tracking_intensity": "medium", "data_retention": 5400}, {"layer_name": "AbismoUmbral", "resolution": 32, "tracking_intensity": "high", "data_r
etention": 3600}], "resolution_settings": {"spatial_resolution": 100.0, "temporal_sampling": 0.5, "data_compression": 0.8}, "visualization_settings": {"enable_realtime": true, "color_scheme": "thermal", "opacity": 0.7, "update_interval": 1}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_3d_heatmap_system
LogAnalytics: Warning: CreateAnalyticsProvider config not contain required parameter APIKeyET
LogTemp: Error: InitializeAnalyticsProvider: Failed to create modern UE 5.6.1 analytics provider AuracronAnalyticsSystem
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "error",
    "error": "Failed to initialize heatmap analytics provider"
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 86
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_advanced_balance_metrics", "params": {"balance_system_name": "AuracronBalanceSystem", "metric_categories": ["tower_control", "objective_control", "layer_utilization", "transition_frequency", "resource_distribution"], "imbalance_thresholds": {"tower_control_deviation": 0.15, "objective_time_deviation": 0.2, "layer_usage_deviation": 0.25, "transition_rate_deviation": 0.3}, "automated_alerts": {"enable_alerts": true, "alert_threshold": 0.2, "notification_interval": 300}, "correction_suggestions": {"enable_suggestions": true, "suggest
ion_confidence": 0.8, "auto_apply": false}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_advanced_balance_metrics
LogTemp: Advanced Balance Metrics: Initialized tracking for tower_control
LogTemp: Advanced Balance Metrics: Initialized tracking for objective_control
LogTemp: Advanced Balance Metrics: Initialized tracking for layer_utilization
LogTemp: Advanced Balance Metrics: Initialized tracking for transition_frequency
LogTemp: Advanced Balance Metrics: Initialized tracking for resource_distribution
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem.AuracronBalanceSystem' could not be found in the Asset Registry.
LogTemp: Advanced Balance Metrics system created: AuracronBalanceSystem (Metrics: 5, Categories: 5, Saved: No)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "balance_system_name": "AuracronBalanceSystem",
        "package_path": "/Game/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem",
        "metrics_initialized": 5,
        "metric_categories_count": 5,
        "saved_to_disk": false,
        "config_path": "../../../../../../Game/AURACRON/Content/Auracron/Analytics/BalanceMetrics/AuracronBalanceSystem_Config.json",
        "configuration":
        {
            "balance_system_name": "AuracronBalanceSystem",
            "metric_categories": [],
            "imbalance_thresholds":
            {
                "tower_control_deviation": 0.14999999999999999,
                "objective_time_deviation": 0.20000000000000001,
                "layer_usage_deviation": 0.25,
                "transition_rate_deviation": 0.29999999999999999
            },
            "automated_alerts":
            {
                "enable_alerts": true,
                "alert_threshold": 0.20000000000000001,
                "notification_interval": 300
            },
            "metrics_initialized": 5
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 922
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "actors": [
            {
                "name": "WorldSettings",
                "class": "WorldSettings",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Brush_0",
                "class": "Brush",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "DefaultPhysicsVolume_0",
                "class": "DefaultPhysicsVolume",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "GameplayDebuggerPlayerManager_0",
                "class": "GameplayDebuggerPlayerManager",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "ChaosDebugDrawActor",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "AbstractNavData-Default",
                "class": "AbstractNavData",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "PlanicieRadiante_Base",
                "class": "StaticMeshActor",
                "location": [ 0, 0, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "FirmamentoZephyr_Base",
                "class": "StaticMeshActor",
                "location": [ 0, 0, 3000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "AbismoUmbral_Base",
                "class": "StaticMeshActor",
                "location": [ 0, 0, 5000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "PlanicieRadiante_Terrain_Planicie_Radiante",
                "class": "Landscape",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "PlanicieRadiante_Terrain_Firmamento_Zephyr",
                "class": "Landscape",
                "location": [ 0, 0, 2000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "PlanicieRadiante_Terrain_Abismo_Umbral",
                "class": "Landscape",
                "location": [ 0, 0, 4000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "FirmamentoZephyr_Terrain_Planicie_Radiante",
                "class": "Landscape",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "FirmamentoZephyr_Terrain_Firmamento_Zephyr",
                "class": "Landscape",
                "location": [ 0, 0, 2000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "FirmamentoZephyr_Terrain_Abismo_Umbral",
                "class": "Landscape",
                "location": [ 0, 0, 4000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "AbismoUmbral_Terrain_Planicie_Radiante",
                "class": "Landscape",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "AbismoUmbral_Terrain_Firmamento_Zephyr",
                "class": "Landscape",
                "location": [ 0, 0, 2000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "AbismoUmbral_Terrain_Abismo_Umbral",
                "class": "Landscape",
                "location": [ 0, 0, 4000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 128, 128, 256 ]
            },
            {
                "name": "TopLane_PlanicieRadiante",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "MidLane_PlanicieRadiante",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "BotLane_PlanicieRadiante",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Top_T1",
                "class": "BP_Torre_Azul_Top_T1_C",
                "location": [ -5000, 5000, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Top_T2",
                "class": "BP_Torre_Azul_Top_T2_C",
                "location": [ -3500, 3500, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Mid_T1",
                "class": "BP_Torre_Azul_Mid_T1_C",
                "location": [ -4000, 0, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Bot_T1",
                "class": "BP_Torre_Azul_Bot_T1_C",
                "location": [ -5000, -5000, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Top_T1",
                "class": "BP_Torre_Vermelha_Top_T1_C",
                "location": [ 5000, -5000, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Mid_T1",
                "class": "BP_Torre_Vermelha_Mid_T1_C",
                "location": [ 4000, 0, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Bot_T1",
                "class": "BP_Torre_Vermelha_Bot_T1_C",
                "location": [ 5000, 5000, 200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Nexus_Azul_PlanicieRadiante",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Nexus_Vermelho_PlanicieRadiante",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Azul_Pequeno_1",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Azul_Medio_1",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Buff_Luz_Azul",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Buff_Crescimento_Azul",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Buff_Luz_Vermelho",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Buff_Crescimento_Vermelho",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Guardiao_da_Aurora",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Top_River",
                "class": "StaticMeshActor",
                "location": [ 0, 6000, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Mid_Center",
                "class": "StaticMeshActor",
                "location": [ 0, 0, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Bot_River",
                "class": "StaticMeshActor",
                "location": [ 0, -6000, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Jungle_NE",
                "class": "StaticMeshActor",
                "location": [ 4500, 4500, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Jungle_NW",
                "class": "StaticMeshActor",
                "location": [ -4500, 4500, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Portal_Jungle_SE",
                "class": "StaticMeshActor",
                "location": [ 4500, -4500, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Elevador_Norte",
                "class": "StaticMeshActor",
                "location": [ 0, 7500, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Elevador_Sul",
                "class": "StaticMeshActor",
                "location": [ 0, -7500, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Elevador_Leste",
                "class": "StaticMeshActor",
                "location": [ 7500, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Elevador_Oeste",
                "class": "StaticMeshActor",
                "location": [ -7500, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Corrente_Vento_Norte",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Corrente_Vento_Sul",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Zephyr_Norte_1",
                "class": "BP_Torre_Azul_Zephyr_Norte_1_C",
                "location": [ -4000, 4000, 2200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Zephyr_Sul_1",
                "class": "BP_Torre_Azul_Zephyr_Sul_1_C",
                "location": [ -4000, -4000, 2200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Zephyr_Norte_1",
                "class": "BP_Torre_Vermelha_Zephyr_Norte_1_C",
                "location": [ 4000, 4000, 2200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Zephyr_Sul_1",
                "class": "BP_Torre_Vermelha_Zephyr_Sul_1_C",
                "location": [ 4000, -4000, 2200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Senhor_dos_Ventos",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Camp_Tempestade_Azul_1",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "JungleStructure_monster_den_L2",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Azul_Umbral_1",
                "class": "BP_Torre_Azul_Umbral_1_C",
                "location": [ -3000, 3000, 4300 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Torre_Vermelha_Umbral_1",
                "class": "BP_Torre_Vermelha_Umbral_1_C",
                "location": [ 3000, -3000, 4300 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Arqui_Sombra",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Luz_PlanicieRadiante_Principal",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Luz_FirmamentoZephyr_Principal",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Luz_AbismoUmbral_Principal",
                "class": "Actor",
                "location": [ 0, 0, 0 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Efeito_PlanicieRadiante_Cristais",
                "class": "Actor",
                "location": [ 0, 0, 1200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Efeito_FirmamentoZephyr_Ventos",
                "class": "Actor",
                "location": [ 0, 0, 3200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "Efeito_AbismoUmbral_Sombras",
                "class": "Actor",
                "location": [ 0, 0, 5200 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "FogOfWar_Layer_0_AuracronFogOfWarSystem",
                "class": "ExponentialHeightFog",
                "location": [ 0, 0, 1000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "FogOfWar_Layer_1_AuracronFogOfWarSystem",
                "class": "ExponentialHeightFog",
                "location": [ 0, 0, 3000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            },
            {
                "name": "FogOfWar_Layer_2_AuracronFogOfWarSystem",
                "class": "ExponentialHeightFog",
                "location": [ 0, 0, 5000 ],
                "rotation": [ 0, 0, 0 ],
                "scale": [ 1, 1, 1 ]
            }
        ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 11984
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Top_T3", "tower_type": "nexus", "location": {"x": -2000.0, "y": 2000.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 1000, "base_radius": 300, "architectural_style": "crystal_fortress", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage", "regeneration"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams", "healing_particles"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Top_T3 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Top_T3 spawned in world at location (-2000.0, 2000.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.BP_Torre_Azul_Top_T3]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Top_T3421FD0A946C93E2685AE4BA9DB667869.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 136.124 ms (total: 2.98 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Top_T3 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Top_T3 (Type: nexus, Layer: 0, Team: 0, Height: 1200.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Top_T3",
        "tower_type": "nexus",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 1200,
        "tower_radius": 200,
        "tower_levels": 7,
        "hierarchical_instancing": true,
        "pcg_generation": true,
        "success": true,
        "timestamp": "2025.08.27-23.05.22",
        "location":
        {
            "x": -2000,
            "y": 2000,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Top_T3 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Mid_T2", "tower_type": "advanced", "location": {"x": -2500.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.49ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Mid_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Mid_T2 spawned in world at location (-2500.0, 0.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.BP_Torre_Azul_Mid_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Mid_T2DD332E2448A94C250008159C24027FE6.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 124.776 ms (total: 3.10 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Mid_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Mid_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Mid_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-23.05.29",
        "location":
        {
            "x": -2500,
            "y": 0,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 455
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Mid_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Azul_Bot_T2", "tower_type": "advanced", "location": {"x": -3500.0, "y": -3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "crystal_light", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["light_aura", "crystal_glow", "energy_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Azul_Bot_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Azul_Bot_T2 spawned in world at location (-3500.0, -3500.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.BP_Torre_Azul_Bot_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Azul_Bot_T2C7C2528946D80F1CE1B39B81CC4816E7.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 116.305 ms (total: 3.22 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Azul_Bot_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Azul_Bot_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Azul_Bot_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-23.05.35",
        "location":
        {
            "x": -3500,
            "y": -3500,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 459
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Azul_Bot_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Top_T2", "tower_type": "advanced", "location": {"x": 3500.0, "y": -3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.59ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Top_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Top_T2 spawned in world at location (3500.0, -3500.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.BP_Torre_Vermelha_Top_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Top_T23319CBEB48C9088D870642AD287BBC4F.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 172.679 ms (total: 3.39 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Top_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Top_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Top_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-23.05.42",
        "location":
        {
            "x": 3500,
            "y": -3500,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 462
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Top_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Mid_T2", "tower_type": "advanced", "location": {"x": 2500.0, "y": 0.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Mid_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Mid_T2 spawned in world at location (2500.0, 0.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.BP_Torre_Vermelha_Mid_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Mid_T273236AFA47B82B33C7842FB8E5F39BDD.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 179.879 ms (total: 3.57 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Mid_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Mid_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Mid_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-23.05.48",
        "location":
        {
            "x": 2500,
            "y": 0,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 458
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Mid_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_structures", "params": {"tower_name": "Torre_Vermelha_Bot_T2", "tower_type": "advanced", "location": {"x": 3500.0, "y": 3500.0, "z": 200.0}, "layer_index": 0, "tower_config": {"height": 900, "base_radius": 250, "architectural_style": "shadow_crystal", "defensive_features": ["energy_shield", "auto_targeting", "splash_damage"], "visual_effects": ["dark_aura", "red_glow", "shadow_beams"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_structures
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_tower_structures
LogJson: Warning: Field team_index was not found.
LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
LogUObjectHash: Compacting FUObjectHashTables data took   1.16ms
LogTemp: CreateRealTowerBlueprint: Successfully created Blueprint BP_Torre_Vermelha_Bot_T2 at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
LogTemp: HandleCreateTowerStructures: Tower Torre_Vermelha_Bot_T2 spawned in world at location (3500.0, 3500.0, 200.0)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2] ([2] browsable assets)...
LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.BP_Torre_Vermelha_Bot_T2]
OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2" FILE="../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/BP_Torre_Vermelha_Bot_T214D25C9346325ABC56E53B81B0B1461B.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2.uasset'
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
LogFileHelpers: InternalPromptForCheckoutAndSave took 191.671 ms (total: 3.77 sec)
LogTemp: HandleCreateTowerStructures: Tower Blueprint Torre_Vermelha_Bot_T2 successfully saved at /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2
LogTemp: HandleCreateTowerStructures: Created tower Torre_Vermelha_Bot_T2 (Type: advanced, Layer: 0, Team: 0, Height: 800.0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_tower_structures",
        "tower_name": "Torre_Vermelha_Bot_T2",
        "tower_type": "advanced",
        "layer_index": 0,
        "team_index": 0,
        "tower_height": 800,
        "tower_radius": 150,
        "tower_levels": 5,
        "hierarchical_instancing": true,
        "pcg_generation": false,
        "success": true,
        "timestamp": "2025.08.27-23.05.55",
        "location":
        {
            "x": 3500,
            "y": 3500,
            "z": 200
        }
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 461
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Auracron/MOBA/Towers/BP_Torre_Vermelha_Bot_T2 Validando ativo
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Pequeno_2", "camp_type": "small", "location": {"x": -2000.0, "y": -2500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "light_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "light_blessing"}, "respawn_timer": 60}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Pequeno_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Pequeno_2 (Type: small, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Azul_Pequeno_2",
        "camp_type": "small",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.06.03"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 227
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Azul_Medio_2", "camp_type": "medium", "location": {"x": -1800.0, "y": 3200.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "crystal_golem", "count": 1, "level": 3}, {"type": "light_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "crystal_armor"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Azul_Medio_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Azul_Medio_2 (Type: medium, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Azul_Medio_2",
        "camp_type": "medium",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.06.11"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vermelho_Pequeno_1", "camp_type": "small", "location": {"x": 3000.0, "y": -2000.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "small", "monster_spawns": [{"type": "shadow_sprite", "count": 3, "level": 1}], "reward_systems": {"gold": 50, "experience": 80, "buff": "shadow_blessing"}, "respawn_timer": 60}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Vermelho_Pequeno_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Vermelho_Pequeno_1 (Type: small, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Vermelho_Pequeno_1",
        "camp_type": "small",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.06.17"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vermelho_Medio_1", "camp_type": "medium", "location": {"x": 2500.0, "y": -3500.0, "z": 150.0}, "layer_index": 0, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "shadow_golem", "count": 1, "level": 3}, {"type": "shadow_sprite", "count": 2, "level": 2}], "reward_systems": {"gold": 120, "experience": 180, "buff": "shadow_armor"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Vermelho_Medio_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Vermelho_Medio_1 (Type: medium, Layer: 0)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Vermelho_Medio_1",
        "camp_type": "medium",
        "layer_index": 0,
        "success": true,
        "timestamp": "2025.08.27-23.06.23"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Azul_2", "camp_type": "medium", "location": {"x": -2500.0, "y": -2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "storm_elemental", "count": 1, "level": 4}, {"type": "wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "storm_speed"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Azul_2 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Azul_2 (Type: medium, Layer: 1)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Tempestade_Azul_2",
        "camp_type": "medium",
        "layer_index": 1,
        "success": true,
        "timestamp": "2025.08.27-23.06.31"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 231
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Vento_Azul_1", "camp_type": "large", "location": {"x": -1800.0, "y": 1800.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "wind_lord_minor", "count": 1, "level": 6}, {"type": "storm_elemental", "count": 2, "level": 4}], "reward_systems": {"gold": 250, "experience": 350, "buff": "wind_mastery_minor"}, "respawn_timer": 180}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Vento_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Vento_Azul_1 (Type: large, Layer: 1)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Vento_Azul_1",
        "camp_type": "large",
        "layer_index": 1,
        "success": true,
        "timestamp": "2025.08.27-23.06.39"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 225
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Tempestade_Vermelho_1", "camp_type": "medium", "location": {"x": 3000.0, "y": 2000.0, "z": 2150.0}, "layer_index": 1, "camp_config": {"camp_size": "medium", "monster_spawns": [{"type": "dark_storm_elemental", "count": 1, "level": 4}, {"type": "shadow_wind_sprite", "count": 2, "level": 3}], "reward_systems": {"gold": 150, "experience": 220, "buff": "dark_storm_speed"}, "respawn_timer": 90}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Tempestade_Vermelho_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Tempestade_Vermelho_1 (Type: medium, Layer: 1)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Tempestade_Vermelho_1",
        "camp_type": "medium",
        "layer_index": 1,
        "success": true,
        "timestamp": "2025.08.27-23.06.45"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 235
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Sombra_Azul_1", "camp_type": "large", "location": {"x": -2000.0, "y": 2500.0, "z": 4250.0}, "layer_index": 2, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "umbral_guardian", "count": 1, "level": 8}, {"type": "shadow_wraith", "count": 3, "level": 6}], "reward_systems": {"gold": 400, "experience": 500, "buff": "umbral_stealth"}, "respawn_timer": 120}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Sombra_Azul_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Sombra_Azul_1 (Type: large, Layer: 2)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Sombra_Azul_1",
        "camp_type": "large",
        "layer_index": 2,
        "success": true,
        "timestamp": "2025.08.27-23.06.52"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 226
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_jungle_camps", "params": {"camp_name": "Camp_Sombra_Vermelho_1", "camp_type": "large", "location": {"x": 2000.0, "y": -2500.0, "z": 4250.0}, "layer_index": 2, "camp_config": {"camp_size": "large", "monster_spawns": [{"type": "void_guardian", "count": 1, "level": 8}, {"type": "void_wraith", "count": 3, "level": 6}], "reward_systems": {"gold": 400, "experience": 500, "buff": "void_stealth"}, "respawn_timer": 120}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_jungle_camps
LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_jungle_camps
LogTemp: CreateRobustTowerStructure: Created tower Camp_Sombra_Vermelho_1 with 1 levels (Height: 200.0, HISM: Yes, PCG: No)
LogTemp: HandleCreateJungleCamps: Created camp Camp_Sombra_Vermelho_1 (Type: large, Layer: 2)
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_jungle_camps",
        "camp_name": "Camp_Sombra_Vermelho_1",
        "camp_type": "large",
        "layer_index": 2,
        "success": true,
        "timestamp": "2025.08.27-23.06.59"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_bridge_meshes", "params": {"bridge_name": "Ponte_Altar_Amanhecer", "bridge_type": "dimensional", "start_location": {"x": 2000.0, "y": 3000.0, "z": 1000.0}, "end_location": {"x": 2000.0, "y": 3000.0, "z": 3000.0}, "bridge_settings": {"bridge_width": 300, "support_pillars": false, "decorative_railings": true, "magical_effects": ["dimensional_energy", "crystal_resonance", "light_bridge"]}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_bridge_meshes
LogTemp: UnrealMCPProceduralMeshCommands::HandleCommand - Command: create_bridge_meshes
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "error",
    "error": "Missing required parameters: bridge_name, start_point, end_point"
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 103
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Altar_Amanhecer", "type": "STATICMESHACTOR", "location": [2000.0, 3000.0, 2000.0], "rotation": [0.0, 0.0, 90.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Ponte_Altar_Amanhecer",
        "class": "StaticMeshActor",
        "location": [ 2000, 3000, 2000 ],
        "rotation": [ 0, 0, 89.999999999999986 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 220
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Templo_Ventos", "type": "STATICMESHACTOR", "location": [3000.0, 2000.0, 3500.0], "rotation": [0.0, 0.0, 45.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Ponte_Templo_Ventos",
        "class": "StaticMeshActor",
        "location": [ 3000, 2000, 3500 ],
        "rotation": [ 0, 0, 45.000000000000028 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 218
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "Ponte_Cora\u00e7\u00e3o_Trevas", "type": "STATICMESHACTOR", "location": [-2000.0, -3000.0, 2500.0], "rotation": [0.0, 0.0, -45.0]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "Ponte_Coração_Trevas",
        "class": "StaticMeshActor",
        "location": [ -2000, -3000, 2500 ],
        "rotation": [ 0, 0, -45 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 206
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted